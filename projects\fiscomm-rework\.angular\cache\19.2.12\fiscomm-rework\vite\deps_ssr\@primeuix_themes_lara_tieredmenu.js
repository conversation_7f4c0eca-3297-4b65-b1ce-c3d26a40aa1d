import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/tieredmenu/index.mjs
var o = {
  background: "{content.background}",
  borderColor: "{content.border.color}",
  color: "{content.color}",
  borderRadius: "{content.border.radius}",
  shadow: "{overlay.navigation.shadow}",
  transitionDuration: "{transition.duration}"
};
var i = {
  padding: "{navigation.list.padding}",
  gap: "{navigation.list.gap}"
};
var n = {
  focusBackground: "{navigation.item.focus.background}",
  activeBackground: "{navigation.item.active.background}",
  color: "{navigation.item.color}",
  focusColor: "{navigation.item.focus.color}",
  activeColor: "{navigation.item.active.color}",
  padding: "{navigation.item.padding}",
  borderRadius: "{navigation.item.border.radius}",
  gap: "{navigation.item.gap}",
  icon: {
    color: "{navigation.item.icon.color}",
    focusColor: "{navigation.item.icon.focus.color}",
    activeColor: "{navigation.item.icon.active.color}"
  }
};
var a = {
  mobileIndent: "1.25rem"
};
var t = {
  size: "{navigation.submenu.icon.size}",
  color: "{navigation.submenu.icon.color}",
  focusColor: "{navigation.submenu.icon.focus.color}",
  activeColor: "{navigation.submenu.icon.active.color}"
};
var r = {
  borderColor: "{content.border.color}"
};
var c = {
  root: o,
  list: i,
  item: n,
  submenu: a,
  submenuIcon: t,
  separator: r
};
export {
  c as default,
  n as item,
  i as list,
  o as root,
  r as separator,
  a as submenu,
  t as submenuIcon
};
//# sourceMappingURL=@primeuix_themes_lara_tieredmenu.js.map
