{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/orderlist/index.ts"], "sourcesContent": ["import type { OrderListDesignTokens, OrderListTokenSections } from '@primeuix/themes/types/orderlist';\n\nexport const root: OrderListTokenSections.Root = {\n    gap: '1.125rem'\n};\n\nexport const controls: OrderListTokenSections.Controls = {\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    controls\n} satisfies OrderListDesignTokens;\n"], "mappings": ";;;;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,UAAA;AACA;", "names": []}