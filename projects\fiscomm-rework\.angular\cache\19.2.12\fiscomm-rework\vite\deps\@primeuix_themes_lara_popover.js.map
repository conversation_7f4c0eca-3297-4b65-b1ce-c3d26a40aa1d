{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/popover/index.ts"], "sourcesContent": ["import type { PopoverDesignTokens, PopoverTokenSections } from '@primeuix/themes/types/popover';\n\nexport const root: PopoverTokenSections.Root = {\n    background: '{overlay.popover.background}',\n    borderColor: '{overlay.popover.border.color}',\n    color: '{overlay.popover.color}',\n    borderRadius: '{overlay.popover.border.radius}',\n    shadow: '{overlay.popover.shadow}',\n    gutter: '10px',\n    arrowOffset: '1.25rem'\n};\n\nexport const content: PopoverTokenSections.Content = {\n    padding: '{overlay.popover.padding}'\n};\n\nexport default {\n    root,\n    content\n} satisfies PopoverDesignTokens;\n"], "mappings": ";;;;EAEa,YAAkC;EAC3C,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;EACA,QAAQ;EACR,aAAQ;AACR;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;AACA;", "names": []}