import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/knob/index.mjs
var o = {
  transitionDuration: "{transition.duration}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var r = {
  background: "{primary.color}"
};
var t = {
  background: "{content.border.color}"
};
var n = {
  color: "{text.muted.color}"
};
var c = {
  root: o,
  value: r,
  range: t,
  text: n
};
export {
  c as default,
  t as range,
  o as root,
  n as text,
  r as value
};
//# sourceMappingURL=@primeuix_themes_lara_knob.js.map
