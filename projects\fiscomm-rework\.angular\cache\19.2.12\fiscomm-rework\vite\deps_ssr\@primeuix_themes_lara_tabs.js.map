{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tabs/index.ts"], "sourcesContent": ["import type { TabsDesignTokens, TabsTokenSections } from '@primeuix/themes/types/tabs';\n\nexport const root: TabsTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const tablist: TabsTokenSections.Tablist = {\n    borderWidth: '0',\n    background: '{content.background}',\n    borderColor: '{content.border.color}'\n};\n\nexport const tab: TabsTokenSections.Tab = {\n    borderWidth: '2px 0 0 0',\n    borderColor: 'transparent',\n    hoverBorderColor: 'transparent',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}',\n    padding: '1rem 1.25rem',\n    fontWeight: '700',\n    margin: '0',\n    gap: '0.5rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const tabpanel: TabsTokenSections.Tabpanel = {\n    background: '{content.background}',\n    color: '{content.color}',\n    padding: '0.875rem 1.125rem 1.125rem 1.125rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const navButton: TabsTokenSections.NavButton = {\n    background: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    width: '2.5rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const activeBar: TabsTokenSections.ActiveBar = {\n    height: '0',\n    bottom: '0',\n    background: 'transparent'\n};\n\nexport const colorScheme: TabsTokenSections.ColorScheme = {\n    light: {\n        navButton: {\n            shadow: '0px 0px 10px 50px rgba(255, 255, 255, 0.6)'\n        },\n        tab: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            activeBackground: '{surface.0}'\n        }\n    },\n    dark: {\n        navButton: {\n            shadow: '0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)'\n        },\n        tab: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    tablist,\n    tab,\n    tabpanel,\n    navButton,\n    activeBar,\n    colorScheme\n} satisfies TabsDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAA+B;AACxC;IACJ,IAAA;EAEa,aAAqC;EAC9C,YAAa;EACb,aAAY;AACZ;IACJ,IAAA;EAEa,aAA6B;EACtC,aAAa;EACb,kBAAa;EACb,mBAAkB;EAClB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,YAAS;EACT,QAAA;EACA,KAAA;EACA,WAAK;IACL,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,YAAuC;EAChD,OAAA;EACA,SAAO;EACP,WAAS;IACT,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,YAAyC;EAClD,OAAA;EACA,YAAO;EACP,OAAA;EACA,WAAO;IACP,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,QAAA;EACT,QAAQ;EACR,YAAQ;AACR;IACJ,IAAA;EAEa,OAAA;IACT,WAAO;MACH,QAAW;IACP;IACJ,KAAA;MACA,YAAK;MACD,iBAAY;MACZ,kBAAiB;IACjB;EACJ;EACJ,MAAA;IACA,WAAM;MACF,QAAW;IACP;IACJ,KAAA;MACA,YAAK;MACD,iBAAY;MACZ,kBAAiB;IACjB;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,KAAA;EACA,UAAA;EACA,WAAA;EACA,WAAA;EACA,aAAA;AACA;", "names": []}