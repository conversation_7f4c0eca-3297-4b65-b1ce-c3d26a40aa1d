{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/progressbar/index.ts"], "sourcesContent": ["import type { ProgressBarDesignTokens, ProgressBarTokenSections } from '@primeuix/themes/types/progressbar';\n\nexport const root: ProgressBarTokenSections.Root = {\n    background: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    height: '1.5rem'\n};\n\nexport const value: ProgressBarTokenSections.Value = {\n    background: '{primary.color}'\n};\n\nexport const label: ProgressBarTokenSections.Label = {\n    color: '{primary.contrast.color}',\n    fontSize: '0.875rem',\n    fontWeight: '600'\n};\n\nexport default {\n    root,\n    value,\n    label\n} satisfies ProgressBarDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAsC;EAC/C,cAAY;EACZ,QAAA;AACA;IACJ,IAAA;EAEa,YAAwC;AACjD;IACJ,IAAA;EAEa,OAAA;EACT,UAAO;EACP,YAAU;AACV;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,OAAA;AACA;", "names": []}