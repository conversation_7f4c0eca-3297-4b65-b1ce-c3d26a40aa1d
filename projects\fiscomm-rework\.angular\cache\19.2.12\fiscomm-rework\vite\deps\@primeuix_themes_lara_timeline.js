import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/timeline/index.mjs
var e = {
  minHeight: "5rem"
};
var r = {
  eventContent: {
    padding: "1rem 0"
  }
};
var n = {
  eventContent: {
    padding: "0 1rem"
  }
};
var o = {
  size: "1.125rem",
  borderRadius: "50%",
  borderWidth: "2px",
  background: "{content.background}",
  borderColor: "{primary.color}",
  content: {
    borderRadius: "50%",
    size: "0.375rem",
    background: "transparent",
    insetShadow: "none"
  }
};
var t = {
  color: "{content.border.color}",
  size: "2px"
};
var a = {
  event: e,
  horizontal: r,
  vertical: n,
  eventMarker: o,
  eventConnector: t
};
export {
  a as default,
  e as event,
  t as eventConnector,
  o as eventMarker,
  r as horizontal,
  n as vertical
};
//# sourceMappingURL=@primeuix_themes_lara_timeline.js.map
