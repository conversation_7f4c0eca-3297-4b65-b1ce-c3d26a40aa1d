import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/editor/index.mjs
var o = {
  borderColor: "{content.border.color}",
  borderRadius: "{content.border.radius}"
};
var r = {
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  activeColor: "{primary.color}"
};
var e = {
  background: "{overlay.select.background}",
  borderColor: "{overlay.select.border.color}",
  borderRadius: "{overlay.select.border.radius}",
  color: "{overlay.select.color}",
  shadow: "{overlay.select.shadow}",
  padding: "{list.padding}"
};
var l = {
  focusBackground: "{list.option.focus.background}",
  color: "{list.option.color}",
  focusColor: "{list.option.focus.color}",
  padding: "{list.option.padding}",
  borderRadius: "{list.option.border.radius}"
};
var t = {
  background: "{content.background}",
  borderColor: "{content.border.color}",
  color: "{content.color}",
  borderRadius: "{content.border.radius}"
};
var c = {
  light: {
    toolbar: {
      background: "{surface.50}"
    }
  },
  dark: {
    toolbar: {
      background: "{surface.800}"
    }
  }
};
var d = {
  toolbar: o,
  toolbarItem: r,
  overlay: e,
  overlayOption: l,
  content: t,
  colorScheme: c
};
export {
  c as colorScheme,
  t as content,
  d as default,
  e as overlay,
  l as overlayOption,
  o as toolbar,
  r as toolbarItem
};
//# sourceMappingURL=@primeuix_themes_lara_editor.js.map
