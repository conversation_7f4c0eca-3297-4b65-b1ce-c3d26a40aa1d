{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/fileupload/index.ts"], "sourcesContent": ["import type { FileUploadDesignTokens, FileUploadTokenSections } from '@primeuix/themes/types/fileupload';\n\nexport const root: FileUploadTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const header: FileUploadTokenSections.Header = {\n    borderWidth: '0 0 1px 0',\n    borderColor: '{content.border.color}',\n    padding: '1.125rem',\n    borderRadius: '5px 5px 0 0',\n    gap: '0.5rem'\n};\n\nexport const content: FileUploadTokenSections.Content = {\n    highlightBorderColor: '{primary.color}',\n    padding: '1.125rem',\n    gap: '1rem'\n};\n\nexport const file: FileUploadTokenSections.File = {\n    padding: '1rem',\n    gap: '1rem',\n    borderColor: '{content.border.color}',\n    info: {\n        gap: '0.5rem'\n    }\n};\n\nexport const fileList: FileUploadTokenSections.FileList = {\n    gap: '0.5rem'\n};\n\nexport const progressbar: FileUploadTokenSections.Progressbar = {\n    height: '0.25rem'\n};\n\nexport const basic: FileUploadTokenSections.Basic = {\n    gap: '0.5rem'\n};\n\nexport const colorScheme: FileUploadTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    content,\n    file,\n    fileList,\n    progressbar,\n    basic,\n    colorScheme\n} satisfies FileUploadDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAqC;EAC9C,aAAY;EACZ,OAAA;EACA,cAAO;EACP,oBAAc;AACd;IACJ,IAAA;EAEa,aAAyC;EAClD,aAAa;EACb,SAAA;EACA,cAAS;EACT,KAAA;AACA;IACJ,IAAA;EAEa,sBAA2C;EACpD,SAAA;EACA,KAAA;AACA;IACJ,IAAA;EAEa,SAAqC;EAC9C,KAAA;EACA,aAAK;EACL,MAAA;IACA,KAAM;EACF;AACJ;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,QAAA;AACT;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,OAAA;IACA;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,SAAA;EACA,MAAA;EACA,UAAA;EACA,aAAA;EACA,OAAA;EACA,aAAA;AACA;", "names": []}