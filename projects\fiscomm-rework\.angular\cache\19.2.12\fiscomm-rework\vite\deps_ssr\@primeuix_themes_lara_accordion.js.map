{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/accordion/index.ts"], "sourcesContent": ["import type { AccordionDesignTokens, AccordionTokenSections } from '@primeuix/themes/types/accordion';\n\nexport const root: AccordionTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const panel: AccordionTokenSections.Panel = {\n    borderWidth: '0',\n    borderColor: '{content.border.color}'\n};\n\nexport const header: AccordionTokenSections.Header = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{text.color}',\n    padding: '1.125rem',\n    fontWeight: '700',\n    borderRadius: '0',\n    borderWidth: '0 1px 1px 1px',\n    borderColor: '{content.border.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    },\n    toggleIcon: {\n        color: '{text.muted.color}',\n        hoverColor: '{text.color}',\n        activeColor: '{text.color}',\n        activeHoverColor: '{text.color}'\n    },\n    first: {\n        topBorderRadius: '{content.border.radius}',\n        borderWidth: '1px'\n    },\n    last: {\n        bottomBorderRadius: '{content.border.radius}',\n        activeBottomBorderRadius: '0'\n    }\n};\n\nexport const content: AccordionTokenSections.Content = {\n    borderWidth: '0 1px 1px 1px',\n    borderColor: '{content.border.color}',\n    background: '{content.background}',\n    color: '{text.color}',\n    padding: '1.125rem'\n};\n\nexport const colorScheme: AccordionTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            activeBackground: '{surface.50}',\n            activeHoverBackground: '{surface.100}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.800}',\n            activeHoverBackground: '{surface.700}'\n        }\n    }\n};\n\nexport default {\n    root,\n    panel,\n    header,\n    content,\n    colorScheme\n} satisfies AccordionDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAoC;AAC7C;IACJ,IAAA;EAEa,aAAsC;EAC/C,aAAa;AACb;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,aAAY;EACZ,SAAA;EACA,YAAS;EACT,cAAY;EACZ,aAAc;EACd,aAAa;EACb,WAAa;IACb,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,YAAA;IACA,OAAA;IACI,YAAO;IACP,aAAY;IACZ,kBAAa;EACb;EACJ,OAAA;IACA,iBAAO;IACH,aAAA;EACA;EACJ,MAAA;IACA,oBAAM;IACF,0BAAoB;EACpB;AACJ;IACJ,IAAA;EAEa,aAA0C;EACnD,aAAa;EACb,YAAa;EACb,OAAA;EACA,SAAO;AACP;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,iBAAY;MACZ,kBAAiB;MACjB,uBAAkB;IAClB;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,iBAAY;MACZ,kBAAiB;MACjB,uBAAkB;IAClB;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,QAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}