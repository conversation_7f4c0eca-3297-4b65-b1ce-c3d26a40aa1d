{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/splitter/index.ts"], "sourcesContent": ["import type { SplitterDesignTokens, SplitterTokenSections } from '@primeuix/themes/types/splitter';\n\nexport const root: SplitterTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const gutter: SplitterTokenSections.Gutter = {\n    background: '{content.border.color}'\n};\n\nexport const handle: SplitterTokenSections.Handle = {\n    size: '24px',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: SplitterTokenSections.ColorScheme = {\n    light: {\n        handle: {\n            background: '{surface.400}'\n        }\n    },\n    dark: {\n        handle: {\n            background: '{surface.600}'\n        }\n    }\n};\n\nexport default {\n    root,\n    gutter,\n    handle,\n    colorScheme\n} satisfies SplitterDesignTokens;\n"], "mappings": ";;;;EAEa,YAAmC;EAC5C,aAAY;EACZ,OAAA;EACA,oBAAO;AACP;IACJ,IAAA;EAEa,YAAuC;AAChD;IACJ,IAAA;EAEa,MAAA;EACT,cAAM;EACN,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;IACJ;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,QAAA;EACA,aAAA;AACA;", "names": []}