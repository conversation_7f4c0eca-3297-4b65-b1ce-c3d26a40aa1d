{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/menubar/index.ts"], "sourcesContent": ["import type { MenubarDesignTokens, MenubarTokenSections } from '@primeuix/themes/types/menubar';\n\nexport const root: MenubarTokenSections.Root = {\n    borderColor: 'transparent',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    gap: '0.5rem',\n    padding: '0.75rem 1rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const baseItem: MenubarTokenSections.BaseItem = {\n    borderRadius: '{content.border.radius}',\n    padding: '0.75rem 1rem'\n};\n\nexport const item: MenubarTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const submenu: MenubarTokenSections.Submenu = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}',\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    mobileIndent: '1.25rem',\n    icon: {\n        size: '{navigation.submenu.icon.size}',\n        color: '{navigation.submenu.icon.color}',\n        focusColor: '{navigation.submenu.icon.focus.color}',\n        activeColor: '{navigation.submenu.icon.active.color}'\n    }\n};\n\nexport const separator: MenubarTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport const mobileButton: MenubarTokenSections.MobileButton = {\n    borderRadius: '50%',\n    size: '2rem',\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    hoverBackground: '{content.hover.background}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: MenubarTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.50}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    root,\n    baseItem,\n    item,\n    submenu,\n    separator,\n    mobileButton,\n    colorScheme\n} satisfies MenubarDesignTokens;\n"], "mappings": ";;;;EAEa,aAAkC;EAC3C,cAAa;EACb,OAAA;EACA,KAAO;EACP,SAAK;EACL,oBAAS;AACT;IACJ,IAAA;EAEa,cAA0C;EACnD,SAAA;AACA;IACJ,IAAA;EAEa,iBAAkC;EAC3C,kBAAiB;EACjB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,cAAS;EACT,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;IACP,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;EACA,YAAK;EACL,aAAY;EACZ,cAAa;EACb,QAAA;EACA,cAAQ;EACR,MAAA;IACA,MAAM;IACF,OAAM;IACN,YAAO;IACP,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,aAA4C;AACrD;IACJ,IAAA;EAEa,cAAA;EACT,MAAA;EACA,OAAM;EACN,YAAO;EACP,iBAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;IACF;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;IACF;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,UAAA;EACA,MAAA;EACA,SAAA;EACA,WAAA;EACA,cAAA;EACA,aAAA;AACA;", "names": []}