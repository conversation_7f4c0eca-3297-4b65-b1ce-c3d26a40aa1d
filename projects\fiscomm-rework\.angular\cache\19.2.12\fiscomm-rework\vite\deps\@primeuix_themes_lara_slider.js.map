{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/slider/index.ts"], "sourcesContent": ["import type { SliderDesignTokens, SliderTokenSections } from '@primeuix/themes/types/slider';\n\nexport const root: SliderTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const track: SliderTokenSections.Track = {\n    background: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    size: '3px'\n};\n\nexport const range: SliderTokenSections.Range = {\n    background: '{primary.color}'\n};\n\nexport const handle: SliderTokenSections.Handle = {\n    width: '16px',\n    height: '16px',\n    borderRadius: '50%',\n    background: '{primary.color}',\n    hoverBackground: '{primary.color}',\n    content: {\n        borderRadius: '50%',\n        hoverBackground: '{primary.color}',\n        width: '12px',\n        height: '12px',\n        shadow: 'none'\n    },\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: SliderTokenSections.ColorScheme = {\n    light: {\n        handle: {\n            content: {\n                background: '{surface.0}'\n            }\n        }\n    },\n    dark: {\n        handle: {\n            content: {\n                background: '{surface.950}'\n            }\n        }\n    }\n};\n\nexport default {\n    root,\n    track,\n    range,\n    handle,\n    colorScheme\n} satisfies SliderDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAiC;AAC1C;IACJ,IAAA;EAEa,YAAmC;EAC5C,cAAY;EACZ,MAAA;AACA;IACJ,IAAA;EAEa,YAAmC;AAC5C;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,cAAQ;EACR,YAAc;EACd,iBAAY;EACZ,SAAA;IACA,cAAS;IACL,iBAAc;IACd,OAAA;IACA,QAAO;IACP,QAAQ;EACR;EACJ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,SAAQ;QACJ,YAAS;MACL;IACJ;EACJ;EACJ,MAAA;IACA,QAAM;MACF,SAAQ;QACJ,YAAS;MACL;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,OAAA;EACA,QAAA;EACA,aAAA;AACA;", "names": []}