{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/carousel/index.ts"], "sourcesContent": ["import type { CarouselDesignTokens, CarouselTokenSections } from '@primeuix/themes/types/carousel';\n\nexport const root: CarouselTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const content: CarouselTokenSections.Content = {\n    gap: '0.25rem'\n};\n\nexport const indicatorList: CarouselTokenSections.IndicatorList = {\n    padding: '1rem',\n    gap: '0.5rem'\n};\n\nexport const indicator: CarouselTokenSections.Indicator = {\n    width: '1rem',\n    height: '1rem',\n    borderRadius: '50',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: CarouselTokenSections.ColorScheme = {\n    light: {\n        indicator: {\n            background: '{surface.200}',\n            hoverBackground: '{surface.300}',\n            activeBackground: '{primary.color}'\n        }\n    },\n    dark: {\n        indicator: {\n            background: '{surface.700}',\n            hoverBackground: '{surface.600}',\n            activeBackground: '{primary.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    content,\n    indicatorList,\n    indicator,\n    colorScheme\n} satisfies CarouselDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAmC;AAC5C;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,cAAQ;EACR,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,WAAO;MACH,YAAW;MACP,iBAAY;MACZ,kBAAiB;IACjB;EACJ;EACJ,MAAA;IACA,WAAM;MACF,YAAW;MACP,iBAAY;MACZ,kBAAiB;IACjB;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,eAAA;EACA,WAAA;EACA,aAAA;AACA;", "names": []}