import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/metergroup/index.mjs
var e = {
  borderRadius: "{content.border.radius}",
  gap: "1rem"
};
var r = {
  background: "{content.border.color}",
  size: "0.625rem"
};
var a = {
  gap: "0.5rem"
};
var o = {
  size: "0.5rem"
};
var l = {
  size: "1rem"
};
var t = {
  verticalGap: "0.5rem",
  horizontalGap: "1rem"
};
var b = {
  root: e,
  meters: r,
  label: a,
  labelMarker: o,
  labelIcon: l,
  labelList: t
};
export {
  b as default,
  a as label,
  l as labelIcon,
  t as labelList,
  o as labelMarker,
  r as meters,
  e as root
};
//# sourceMappingURL=@primeuix_themes_lara_metergroup.js.map
