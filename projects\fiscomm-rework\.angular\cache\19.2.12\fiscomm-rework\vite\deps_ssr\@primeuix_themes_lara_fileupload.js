import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/fileupload/index.mjs
var r = {
  background: "{content.background}",
  borderColor: "{content.border.color}",
  color: "{content.color}",
  borderRadius: "{content.border.radius}",
  transitionDuration: "{transition.duration}"
};
var o = {
  borderWidth: "0 0 1px 0",
  borderColor: "{content.border.color}",
  padding: "1.125rem",
  borderRadius: "5px 5px 0 0",
  gap: "0.5rem"
};
var e = {
  highlightBorderColor: "{primary.color}",
  padding: "1.125rem",
  gap: "1rem"
};
var a = {
  padding: "1rem",
  gap: "1rem",
  borderColor: "{content.border.color}",
  info: {
    gap: "0.5rem"
  }
};
var t = {
  gap: "0.5rem"
};
var d = {
  height: "0.25rem"
};
var n = {
  gap: "0.5rem"
};
var c = {
  light: {
    header: {
      background: "{surface.50}",
      color: "{text.color}"
    }
  },
  dark: {
    header: {
      background: "{surface.800}",
      color: "{text.color}"
    }
  }
};
var i = {
  root: r,
  header: o,
  content: e,
  file: a,
  fileList: t,
  progressbar: d,
  basic: n,
  colorScheme: c
};
export {
  n as basic,
  c as colorScheme,
  e as content,
  i as default,
  a as file,
  t as fileList,
  o as header,
  d as progressbar,
  r as root
};
//# sourceMappingURL=@primeuix_themes_lara_fileupload.js.map
