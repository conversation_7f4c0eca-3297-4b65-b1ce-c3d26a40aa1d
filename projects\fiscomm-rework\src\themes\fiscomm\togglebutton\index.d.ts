import type { ToggleButtonTokenSections } from '@primeuix/themes/types/togglebutton';

export * from '@primeuix/themes/types/togglebutton';

declare const root: ToggleButtonTokenSections.Root;
declare const icon: ToggleButtonTokenSections.Icon;
declare const content: ToggleButtonTokenSections.Content;
declare const colorScheme: ToggleButtonTokenSections.ColorScheme;
declare const _default: {
    root: ToggleButtonTokenSections.Root;
    icon: ToggleButtonTokenSections.Icon;
    content: ToggleButtonTokenSections.Content;
    colorScheme: ToggleButtonTokenSections.ColorScheme;
};

export { colorScheme, content, _default as default, icon, root };
