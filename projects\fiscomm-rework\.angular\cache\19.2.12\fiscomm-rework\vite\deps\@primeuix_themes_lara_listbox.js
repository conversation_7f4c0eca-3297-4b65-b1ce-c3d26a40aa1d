import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/listbox/index.mjs
var o = {
  background: "{form.field.background}",
  disabledBackground: "{form.field.disabled.background}",
  borderColor: "{form.field.border.color}",
  invalidBorderColor: "{form.field.invalid.border.color}",
  color: "{form.field.color}",
  disabledColor: "{form.field.disabled.color}",
  shadow: "{form.field.shadow}",
  borderRadius: "{form.field.border.radius}",
  transitionDuration: "{form.field.transition.duration}"
};
var r = {
  padding: "{list.padding}",
  gap: "{list.gap}",
  header: {
    padding: "{list.header.padding}"
  }
};
var d = {
  focusBackground: "{list.option.focus.background}",
  selectedBackground: "{list.option.selected.background}",
  selectedFocusBackground: "{list.option.selected.focus.background}",
  color: "{list.option.color}",
  focusColor: "{list.option.focus.color}",
  selectedColor: "{list.option.selected.color}",
  selectedFocusColor: "{list.option.selected.focus.color}",
  padding: "{list.option.padding}",
  borderRadius: "{list.option.border.radius}"
};
var i = {
  background: "{list.option.group.background}",
  color: "{list.option.group.color}",
  fontWeight: "{list.option.group.font.weight}",
  padding: "{list.option.group.padding}"
};
var t = {
  color: "{list.option.color}",
  gutterStart: "-0.5rem",
  gutterEnd: "0.5rem"
};
var e = {
  padding: "{list.option.padding}"
};
var l = {
  light: {
    option: {
      stripedBackground: "{surface.50}"
    }
  },
  dark: {
    option: {
      stripedBackground: "{surface.900}"
    }
  }
};
var n = {
  root: o,
  list: r,
  option: d,
  optionGroup: i,
  checkmark: t,
  emptyMessage: e,
  colorScheme: l
};
export {
  t as checkmark,
  l as colorScheme,
  n as default,
  e as emptyMessage,
  r as list,
  d as option,
  i as optionGroup,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_listbox.js.map
