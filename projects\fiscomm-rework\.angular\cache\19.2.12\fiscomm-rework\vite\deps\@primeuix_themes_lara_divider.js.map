{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/divider/index.ts"], "sourcesContent": ["import type { DividerDesignTokens, DividerTokenSections } from '@primeuix/themes/types/divider';\n\nexport const root: DividerTokenSections.Root = {\n    borderColor: '{content.border.color}'\n};\n\nexport const content: DividerTokenSections.Content = {\n    background: '{content.background}',\n    color: '{text.color}'\n};\n\nexport const horizontal: DividerTokenSections.Horizontal = {\n    margin: '1.125rem 0',\n    padding: '0 1.125rem',\n    content: {\n        padding: '0 0.625rem'\n    }\n};\n\nexport const vertical: DividerTokenSections.Vertical = {\n    margin: '0 1.125rem',\n    padding: '1.125rem 0',\n    content: {\n        padding: '0.625rem 0'\n    }\n};\n\nexport default {\n    root,\n    content,\n    horizontal,\n    vertical\n} satisfies DividerDesignTokens;\n"], "mappings": ";;;;EAEa,aAAkC;AAC3C;IACJ,IAAA;EAEa,YAAwC;EACjD,OAAA;AACA;IACJ,IAAA;EAEa,QAAA;EACT,SAAQ;EACR,SAAS;IACT,SAAS;EACL;AACJ;IACJ,IAAA;EAEa,QAAA;EACT,SAAQ;EACR,SAAS;IACT,SAAS;EACL;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,YAAA;EACA,UAAA;AACA;", "names": []}