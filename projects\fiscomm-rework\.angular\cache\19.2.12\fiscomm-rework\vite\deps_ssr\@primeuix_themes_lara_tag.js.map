{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tag/index.ts"], "sourcesContent": ["import type { TagDesignTokens, TagTokenSections } from '@primeuix/themes/types/tag';\n\nexport const root: TagTokenSections.Root = {\n    fontSize: '0.875rem',\n    fontWeight: '700',\n    padding: '0.25rem 0.5rem',\n    gap: '0.25rem',\n    borderRadius: '{content.border.radius}',\n    roundedBorderRadius: '{border.radius.xl}'\n};\n\nexport const icon: TagTokenSections.Icon = {\n    size: '0.75rem'\n};\n\nexport const colorScheme: TagTokenSections.ColorScheme = {\n    light: {\n        primary: {\n            background: '{primary.color}',\n            color: '{primary.contrast.color}'\n        },\n        secondary: {\n            background: '{surface.100}',\n            color: '{surface.600}'\n        },\n        success: {\n            background: '{green.500}',\n            color: '{surface.0}'\n        },\n        info: {\n            background: '{sky.500}',\n            color: '{surface.0}'\n        },\n        warn: {\n            background: '{orange.500}',\n            color: '{surface.0}'\n        },\n        danger: {\n            background: '{red.500}',\n            color: '{surface.0}'\n        },\n        contrast: {\n            background: '{surface.950}',\n            color: '{surface.0}'\n        }\n    },\n    dark: {\n        primary: {\n            background: '{primary.color}',\n            color: '{primary.contrast.color}'\n        },\n        secondary: {\n            background: '{surface.800}',\n            color: '{surface.300}'\n        },\n        success: {\n            background: '{green.400}',\n            color: '{green.950}'\n        },\n        info: {\n            background: '{sky.400}',\n            color: '{sky.950}'\n        },\n        warn: {\n            background: '{orange.400}',\n            color: '{orange.950}'\n        },\n        danger: {\n            background: '{red.400}',\n            color: '{red.950}'\n        },\n        contrast: {\n            background: '{surface.0}',\n            color: '{surface.950}'\n        }\n    }\n};\n\nexport default {\n    root,\n    icon,\n    colorScheme\n} satisfies TagDesignTokens;\n"], "mappings": ";;;;;EAEa,UAA8B;EACvC,YAAU;EACV,SAAA;EACA,KAAA;EACA,cAAK;EACL,qBAAc;AACd;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,SAAO;MACH,YAAS;MACL,OAAA;IACA;IACJ,WAAA;MACA,YAAW;MACP,OAAA;IACA;IACJ,SAAA;MACA,YAAS;MACL,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,OAAA;IACA;EACJ;EACJ,MAAA;IACA,SAAM;MACF,YAAS;MACL,OAAA;IACA;IACJ,WAAA;MACA,YAAW;MACP,OAAA;IACA;IACJ,SAAA;MACA,YAAS;MACL,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,aAAA;AACA;", "names": []}