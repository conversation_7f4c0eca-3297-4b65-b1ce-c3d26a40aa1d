import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/paginator/index.mjs
var o = {
  padding: "0.5rem 1rem",
  gap: "0.25rem",
  borderRadius: "{content.border.radius}",
  background: "{content.background}",
  color: "{content.color}",
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "transparent",
  hoverBackground: "{content.hover.background}",
  selectedBackground: "{highlight.background}",
  color: "{text.muted.color}",
  hoverColor: "{text.hover.muted.color}",
  selectedColor: "{highlight.color}",
  width: "2.5rem",
  height: "2.5rem",
  borderRadius: "50%",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var t = {
  color: "{text.muted.color}"
};
var e = {
  maxWidth: "2.5rem"
};
var n = {
  root: o,
  navButton: r,
  currentPageReport: t,
  jumpToPageInput: e
};
export {
  t as currentPageReport,
  n as default,
  e as jumpToPageInput,
  r as navButton,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_paginator.js.map
