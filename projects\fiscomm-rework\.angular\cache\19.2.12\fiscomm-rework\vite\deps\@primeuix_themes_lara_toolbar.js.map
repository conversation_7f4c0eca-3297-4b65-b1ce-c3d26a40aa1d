{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/toolbar/index.ts"], "sourcesContent": ["import type { ToolbarDesignTokens, ToolbarTokenSections } from '@primeuix/themes/types/toolbar';\n\nexport const root: ToolbarTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    gap: '0.5rem',\n    padding: '0.75rem'\n};\n\nexport const colorScheme: ToolbarTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.50}',\n            color: '{content.color}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.800}',\n            color: '{content.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies ToolbarDesignTokens;\n"], "mappings": ";;;;EAEa,YAAkC;EAC3C,aAAY;EACZ,cAAa;EACb,OAAA;EACA,KAAO;EACP,SAAK;AACL;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,OAAA;IACA;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,aAAA;AACA;", "names": []}