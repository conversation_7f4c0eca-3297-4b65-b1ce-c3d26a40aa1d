{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/avatar/index.ts"], "sourcesContent": ["import type { AvatarDesignTokens, AvatarTokenSections } from '@primeuix/themes/types/avatar';\n\nexport const root: AvatarTokenSections.Root = {\n    width: '2rem',\n    height: '2rem',\n    fontSize: '1rem',\n    background: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const icon: AvatarTokenSections.Icon = {\n    size: '1rem'\n};\n\nexport const group: AvatarTokenSections.Group = {\n    borderColor: '{content.background}',\n    offset: '-0.75rem'\n};\n\nexport const lg: AvatarTokenSections.Lg = {\n    width: '3rem',\n    height: '3rem',\n    fontSize: '1.5rem',\n    icon: {\n        size: '1.5rem'\n    },\n    group: {\n        offset: '-1rem'\n    }\n};\n\nexport const xl: AvatarTokenSections.Xl = {\n    width: '4rem',\n    height: '4rem',\n    fontSize: '2rem',\n    icon: {\n        size: '2rem'\n    },\n    group: {\n        offset: '-1.5rem'\n    }\n};\n\nexport default {\n    root,\n    icon,\n    group,\n    lg,\n    xl\n} satisfies AvatarDesignTokens;\n"], "mappings": ";;;;;EAEa,OAAiC;EAC1C,QAAO;EACP,UAAQ;EACR,YAAU;EACV,OAAA;EACA,cAAO;AACP;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,aAAmC;EAC5C,QAAA;AACA;IACJ,IAAA;EAEa,OAA6B;EACtC,QAAO;EACP,UAAQ;EACR,MAAA;IACA,MAAM;EACF;EACJ,OAAA;IACA,QAAO;EACH;AACJ;IACJ,IAAA;EAEa,OAA6B;EACtC,QAAO;EACP,UAAQ;EACR,MAAA;IACA,MAAM;EACF;EACJ,OAAA;IACA,QAAO;EACH;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,OAAA;EACA,IAAA;EACA,IAAA;AACA;", "names": []}