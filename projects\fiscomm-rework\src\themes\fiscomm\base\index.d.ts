/**
 *
 * Lara Base Tokens
 *
 * @module base
 *
 */

/**
 * Design Token Sections
 */
export declare namespace LaraBaseTokenSections {
    interface Primitive {
        borderRadius?: {
            none?: string;
            xs?: string;
            sm?: string;
            md?: string;
            lg?: string;
            xl?: string;
        };
        emerald?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        green?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        lime?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        red?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        orange?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        amber?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        yellow?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        teal?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        cyan?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        sky?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        blue?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        indigo?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        violet?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        purple?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        fuchsia?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        pink?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        rose?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        slate?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        gray?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        zinc?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        neutral?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        stone?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
    }

    interface Semantic {
        transitionDuration?: string;
        focusRing?: {
            width?: string;
            style?: string;
            color?: string;
            offset?: string;
        };
        disabledOpacity?: string;
        iconSize?: string;
        anchorGutter?: string;
        primary?: {
            50?: string;
            100?: string;
            200?: string;
            300?: string;
            400?: string;
            500?: string;
            600?: string;
            700?: string;
            800?: string;
            900?: string;
            950?: string;
        };
        formField?: {
            paddingX?: string;
            paddingY?: string;
            sm?: {
                fontSize?: string;
                paddingX?: string;
                paddingY?: string;
            };
            lg?: {
                fontSize?: string;
                paddingX?: string;
                paddingY?: string;
            };
            borderRadius?: string;
            focusRing?: {
                width?: string;
                style?: string;
                color?: string;
                offset?: string;
                shadow?: string;
            };
            transitionDuration?: string;
        };
        list?: {
            padding?: string;
            gap?: string;
            header?: {
                padding?: string;
            };
            option?: {
                padding?: string;
                borderRadius?: string;
            };
            optionGroup?: {
                padding?: string;
                fontWeight?: string;
            };
        };
        content?: {
            borderRadius?: string;
        };
        mask?: {
            transitionDuration?: string;
        };
        navigation?: {
            list?: {
                padding?: string;
                gap?: string;
            };
            item?: {
                padding?: string;
                borderRadius?: string;
                gap?: string;
            };
            submenuLabel?: {
                padding?: string;
                fontWeight?: string;
            };
            submenuIcon?: {
                size?: string;
            };
        };
        overlay?: {
            select?: {
                borderRadius?: string;
                shadow?: string;
            };
            popover?: {
                borderRadius?: string;
                padding?: string;
                shadow?: string;
            };
            modal?: {
                borderRadius?: string;
                padding?: string;
                shadow?: string;
            };
            navigation?: {
                shadow?: string;
            };
        };
        colorScheme?: {
            light?: {
                surface?: {
                    0?: string;
                    50?: string;
                    100?: string;
                    200?: string;
                    300?: string;
                    400?: string;
                    500?: string;
                    600?: string;
                    700?: string;
                    800?: string;
                    900?: string;
                    950?: string;
                };
                primary?: {
                    color?: string;
                    contrastColor?: string;
                    hoverColor?: string;
                    activeColor?: string;
                };
                highlight?: {
                    background?: string;
                    focusBackground?: string;
                    color?: string;
                    focusColor?: string;
                };
                focusRing?: {
                    shadow?: string;
                };
                mask?: {
                    background?: string;
                    color?: string;
                };
                formField?: {
                    background?: string;
                    disabledBackground?: string;
                    filledBackground?: string;
                    filledHoverBackground?: string;
                    filledFocusBackground?: string;
                    borderColor?: string;
                    hoverBorderColor?: string;
                    focusBorderColor?: string;
                    invalidBorderColor?: string;
                    color?: string;
                    disabledColor?: string;
                    placeholderColor?: string;
                    invalidPlaceholderColor?: string;
                    floatLabelColor?: string;
                    floatLabelFocusColor?: string;
                    floatLabelActiveColor?: string;
                    floatLabelInvalidColor?: string;
                    iconColor?: string;
                    shadow?: string;
                };
                text?: {
                    color?: string;
                    hoverColor?: string;
                    mutedColor?: string;
                    hoverMutedColor?: string;
                };
                content?: {
                    background?: string;
                    hoverBackground?: string;
                    borderColor?: string;
                    color?: string;
                    hoverColor?: string;
                };
                overlay?: {
                    select?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                    popover?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                    modal?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                };
                list?: {
                    option?: {
                        focusBackground?: string;
                        selectedBackground?: string;
                        selectedFocusBackground?: string;
                        color?: string;
                        focusColor?: string;
                        selectedColor?: string;
                        selectedFocusColor?: string;
                        icon?: {
                            color?: string;
                            focusColor?: string;
                        };
                    };
                    optionGroup?: {
                        background?: string;
                        color?: string;
                    };
                };
                navigation?: {
                    item?: {
                        focusBackground?: string;
                        activeBackground?: string;
                        color?: string;
                        focusColor?: string;
                        activeColor?: string;
                        icon?: {
                            color?: string;
                            focusColor?: string;
                            activeColor?: string;
                        };
                    };
                    submenuLabel?: {
                        background?: string;
                        color?: string;
                    };
                    submenuIcon?: {
                        color?: string;
                        focusColor?: string;
                        activeColor?: string;
                    };
                };
            };
            dark?: {
                surface?: {
                    0?: string;
                    50?: string;
                    100?: string;
                    200?: string;
                    300?: string;
                    400?: string;
                    500?: string;
                    600?: string;
                    700?: string;
                    800?: string;
                    900?: string;
                    950?: string;
                };
                primary?: {
                    color?: string;
                    contrastColor?: string;
                    hoverColor?: string;
                    activeColor?: string;
                };
                highlight?: {
                    background?: string;
                    focusBackground?: string;
                    color?: string;
                    focusColor?: string;
                };
                focusRing?: {
                    shadow?: string;
                };
                mask?: {
                    background?: string;
                    color?: string;
                };
                formField?: {
                    background?: string;
                    disabledBackground?: string;
                    filledBackground?: string;
                    filledHoverBackground?: string;
                    filledFocusBackground?: string;
                    borderColor?: string;
                    hoverBorderColor?: string;
                    focusBorderColor?: string;
                    invalidBorderColor?: string;
                    color?: string;
                    disabledColor?: string;
                    placeholderColor?: string;
                    invalidPlaceholderColor?: string;
                    floatLabelColor?: string;
                    floatLabelFocusColor?: string;
                    floatLabelActiveColor?: string;
                    floatLabelInvalidColor?: string;
                    iconColor?: string;
                    shadow?: string;
                };
                text?: {
                    color?: string;
                    hoverColor?: string;
                    mutedColor?: string;
                    hoverMutedColor?: string;
                };
                content?: {
                    background?: string;
                    hoverBackground?: string;
                    borderColor?: string;
                    color?: string;
                    hoverColor?: string;
                };
                overlay?: {
                    select?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                    popover?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                    modal?: {
                        background?: string;
                        borderColor?: string;
                        color?: string;
                    };
                };
                list?: {
                    option?: {
                        focusBackground?: string;
                        selectedBackground?: string;
                        selectedFocusBackground?: string;
                        color?: string;
                        focusColor?: string;
                        selectedColor?: string;
                        selectedFocusColor?: string;
                        icon?: {
                            color?: string;
                            focusColor?: string;
                        };
                    };
                    optionGroup?: {
                        background?: string;
                        color?: string;
                    };
                };
                navigation?: {
                    item?: {
                        focusBackground?: string;
                        activeBackground?: string;
                        color?: string;
                        focusColor?: string;
                        activeColor?: string;
                        icon?: {
                            color?: string;
                            focusColor?: string;
                            activeColor?: string;
                        };
                    };
                    submenuLabel?: {
                        background?: string;
                        color?: string;
                    };
                    submenuIcon?: {
                        color?: string;
                        focusColor?: string;
                        activeColor?: string;
                    };
                };
            };
        };
    }
}

/**
 *
 * _Lara Base Tokens_
 *
 * @group presets/lara
 * @module base
 * @see
 * --- ---
 * **Compatible Libraries**
 *
 * [![PrimeNG](https://i.postimg.cc/63f7jVST/primeng-icon-24.png)](https://primeng.org/theming/styled)
 * [![PrimeVue](https://i.postimg.cc/cC1vVgyh/primevue-icon-24.png)](https://primevue.org/theming/styled)
 * --- ---
 * **Figma UI Kit**
 *
 * [PrimeNG](https://primeng.org/uikit) | [PrimeVue](https://primevue.org/uikit)
 *
 */
export interface LaraBaseDesignTokens {
    primitive?: LaraBaseTokenSections.Primitive;
    semantic?: LaraBaseTokenSections.Semantic;
}

declare const primitive: LaraBaseTokenSections.Primitive;
declare const semantic: LaraBaseTokenSections.Semantic;
declare const _default: {
    primitive: LaraBaseTokenSections.Primitive;
    semantic: LaraBaseTokenSections.Semantic;
};

export { _default as default, primitive, semantic };
