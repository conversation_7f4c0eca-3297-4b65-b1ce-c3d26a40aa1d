{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/imagecompare/index.ts"], "sourcesContent": ["import type { ImageCompareDesignTokens, ImageCompareTokenSections } from '@primeuix/themes/types/imagecompare';\n\nexport const handle: ImageCompareTokenSections.Handle = {\n    size: '15px',\n    hoverSize: '30px',\n    background: 'rgba(255,255,255,0.3)',\n    hoverBackground: 'rgba(255,255,255,0.3)',\n    borderColor: 'rgba(255,255,255,0.3)',\n    hoverBorderColor: 'rgba(255,255,255,0.3)',\n    borderWidth: '3px',\n    borderRadius: '50%',\n    transitionDuration: '{transition.duration}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: 'rgba(255,255,255,0.3)',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport default {\n    handle\n} satisfies ImageCompareDesignTokens;\n"], "mappings": ";;;;;EAEa,MAAA;EACT,WAAM;EACN,YAAW;EACX,iBAAY;EACZ,aAAA;EACA,kBAAa;EACb,aAAA;EACA,cAAa;EACb,oBAAc;EACd,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEO,QAAA;AACH;", "names": []}