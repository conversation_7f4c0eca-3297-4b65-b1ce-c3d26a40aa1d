{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inlinemessage/index.ts"], "sourcesContent": ["import type { InlineMessageDesignTokens, InlineMessageTokenSections } from '@primeuix/themes/types/inlinemessage';\n\nexport const root: InlineMessageTokenSections.Root = {\n    padding: '{form.field.padding.y} {form.field.padding.x}',\n    borderRadius: '{content.border.radius}',\n    gap: '0.5rem'\n};\n\nexport const text: InlineMessageTokenSections.Text = {\n    fontWeight: '500'\n};\n\nexport const icon: InlineMessageTokenSections.Icon = {\n    size: '1.125rem'\n};\n\nexport const colorScheme: InlineMessageTokenSections.ColorScheme = {\n    light: {\n        info: {\n            background: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            color: '{blue.600}',\n            shadow: 'none'\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            color: '{green.600}',\n            shadow: 'none'\n        },\n        warn: {\n            background: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            color: '{yellow.600}',\n            shadow: 'none'\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            color: '{red.600}',\n            shadow: 'none'\n        },\n        secondary: {\n            background: '{surface.100}',\n            borderColor: '{surface.100}',\n            color: '{surface.600}',\n            shadow: 'none'\n        },\n        contrast: {\n            background: '{surface.900}',\n            borderColor: '{surface.900}',\n            color: '{surface.50}',\n            shadow: 'none'\n        }\n    },\n    dark: {\n        info: {\n            background: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            color: '{blue.500}',\n            shadow: 'none'\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            color: '{green.500}',\n            shadow: 'none'\n        },\n        warn: {\n            background: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            color: '{yellow.500}',\n            shadow: 'none'\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            color: '{red.500}',\n            shadow: 'none'\n        },\n        secondary: {\n            background: '{surface.800}',\n            borderColor: '{surface.800}',\n            color: '{surface.300}',\n            shadow: 'none'\n        },\n        contrast: {\n            background: '{surface.0}',\n            borderColor: '{surface.0}',\n            color: '{surface.950}',\n            shadow: 'none'\n        }\n    }\n};\n\nexport default {\n    root,\n    text,\n    icon,\n    colorScheme\n} satisfies InlineMessageDesignTokens;\n"], "mappings": ";;;;EAEa,SAAwC;EACjD,cAAS;EACT,KAAA;AACA;IACJ,IAAA;EAEa,YAAwC;AACjD;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,QAAO;IACP;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,QAAO;IACP;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,QAAO;IACP;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,MAAA;EACA,aAAA;AACA;", "names": []}