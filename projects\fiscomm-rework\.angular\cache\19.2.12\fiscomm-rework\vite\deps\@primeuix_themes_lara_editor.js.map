{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/editor/index.ts"], "sourcesContent": ["import type { EditorDesignTokens, EditorTokenSections } from '@primeuix/themes/types/editor';\n\nexport const toolbar: EditorTokenSections.Toolbar = {\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const toolbarItem: EditorTokenSections.ToolbarItem = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}'\n};\n\nexport const overlay: EditorTokenSections.Overlay = {\n    background: '{overlay.select.background}',\n    borderColor: '{overlay.select.border.color}',\n    borderRadius: '{overlay.select.border.radius}',\n    color: '{overlay.select.color}',\n    shadow: '{overlay.select.shadow}',\n    padding: '{list.padding}'\n};\n\nexport const overlayOption: EditorTokenSections.OverlayOption = {\n    focusBackground: '{list.option.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const content: EditorTokenSections.Content = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const colorScheme: EditorTokenSections.ColorScheme = {\n    light: {\n        toolbar: {\n            background: '{surface.50}'\n        }\n    },\n    dark: {\n        toolbar: {\n            background: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    toolbar,\n    toolbarItem,\n    overlay,\n    overlayOption,\n    content,\n    colorScheme\n} satisfies EditorDesignTokens;\n"], "mappings": ";;;;EAEa,aAAuC;EAChD,cAAa;AACb;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,YAAuC;EAChD,aAAY;EACZ,cAAa;EACb,OAAA;EACA,QAAO;EACP,SAAQ;AACR;IACJ,IAAA;EAEa,iBAAmD;EAC5D,OAAA;EACA,YAAO;EACP,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,YAAuC;EAChD,aAAY;EACZ,OAAA;EACA,cAAO;AACP;IACJ,IAAA;EAEa,OAAA;IACT,SAAO;MACH,YAAS;IACL;EACJ;EACJ,MAAA;IACA,SAAM;MACF,YAAS;IACL;EACJ;AACJ;IACJ,IAAA;EAEO,SAAA;EACH,aAAA;EACA,SAAA;EACA,eAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}