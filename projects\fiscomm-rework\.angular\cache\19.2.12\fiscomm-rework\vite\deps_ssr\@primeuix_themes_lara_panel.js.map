{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/panel/index.ts"], "sourcesContent": ["import type { PanelDesignTokens, PanelTokenSections } from '@primeuix/themes/types/panel';\n\nexport const root: PanelTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}'\n};\n\nexport const header: PanelTokenSections.Header = {\n    borderWidth: '0 0 1px 0',\n    borderColor: '{content.border.color}',\n    padding: '1.125rem',\n    borderRadius: '5px 5px 0 0'\n};\n\nexport const toggleableHeader: PanelTokenSections.ToggleableHeader = {\n    padding: '0.25rem 1.125rem'\n};\n\nexport const title: PanelTokenSections.Title = {\n    fontWeight: '700'\n};\n\nexport const content: PanelTokenSections.Content = {\n    padding: '1.125rem'\n};\n\nexport const footer: PanelTokenSections.Footer = {\n    padding: '1.125rem'\n};\n\nexport const colorScheme: PanelTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    toggleableHeader,\n    title,\n    content,\n    footer,\n    colorScheme\n} satisfies PanelDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAgC;EACzC,aAAY;EACZ,cAAa;EACb,OAAA;AACA;IACJ,IAAA;EAEa,aAAoC;EAC7C,aAAa;EACb,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,YAAkC;AAC3C;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,SAAoC;AAC7C;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,OAAA;IACA;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,kBAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;EACA,aAAA;AACA;", "names": []}