import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/panelmenu/index.mjs
var o = {
  gap: "0",
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "{content.background}",
  borderColor: "{content.border.color}",
  borderWidth: "1px",
  color: "{content.color}",
  padding: "0.25rem 0.25rem",
  borderRadius: "0",
  first: {
    borderWidth: "1px 1px 0 1px",
    topBorderRadius: "{content.border.radius}"
  },
  last: {
    borderWidth: "0 1px 1px 1px",
    bottomBorderRadius: "{content.border.radius}"
  }
};
var n = {
  focusBackground: "{navigation.item.focus.background}",
  color: "{navigation.item.color}",
  focusColor: "{navigation.item.focus.color}",
  gap: "0.5rem",
  padding: "{navigation.item.padding}",
  borderRadius: "{content.border.radius}",
  icon: {
    color: "{navigation.item.icon.color}",
    focusColor: "{navigation.item.icon.focus.color}"
  }
};
var i = {
  indent: "1rem"
};
var t = {
  color: "{navigation.submenu.icon.color}",
  focusColor: "{navigation.submenu.icon.focus.color}"
};
var a = {
  root: o,
  panel: r,
  item: n,
  submenu: i,
  submenuIcon: t
};
export {
  a as default,
  n as item,
  r as panel,
  o as root,
  i as submenu,
  t as submenuIcon
};
//# sourceMappingURL=@primeuix_themes_lara_panelmenu.js.map
