import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/select/index.mjs
var o = {
  background: "{form.field.background}",
  disabledBackground: "{form.field.disabled.background}",
  filledBackground: "{form.field.filled.background}",
  filledHoverBackground: "{form.field.filled.hover.background}",
  filledFocusBackground: "{form.field.filled.focus.background}",
  borderColor: "{form.field.border.color}",
  hoverBorderColor: "{form.field.hover.border.color}",
  focusBorderColor: "{form.field.focus.border.color}",
  invalidBorderColor: "{form.field.invalid.border.color}",
  color: "{form.field.color}",
  disabledColor: "{form.field.disabled.color}",
  placeholderColor: "{form.field.placeholder.color}",
  invalidPlaceholderColor: "{form.field.invalid.placeholder.color}",
  shadow: "{form.field.shadow}",
  paddingX: "{form.field.padding.x}",
  paddingY: "{form.field.padding.y}",
  borderRadius: "{form.field.border.radius}",
  focusRing: {
    width: "{form.field.focus.ring.width}",
    style: "{form.field.focus.ring.style}",
    color: "{form.field.focus.ring.color}",
    offset: "{form.field.focus.ring.offset}",
    shadow: "{form.field.focus.ring.shadow}"
  },
  transitionDuration: "{form.field.transition.duration}",
  sm: {
    fontSize: "{form.field.sm.font.size}",
    paddingX: "{form.field.sm.padding.x}",
    paddingY: "{form.field.sm.padding.y}"
  },
  lg: {
    fontSize: "{form.field.lg.font.size}",
    paddingX: "{form.field.lg.padding.x}",
    paddingY: "{form.field.lg.padding.y}"
  }
};
var r = {
  width: "2.5rem",
  color: "{form.field.icon.color}"
};
var d = {
  background: "{overlay.select.background}",
  borderColor: "{overlay.select.border.color}",
  borderRadius: "{overlay.select.border.radius}",
  color: "{overlay.select.color}",
  shadow: "{overlay.select.shadow}"
};
var l = {
  padding: "{list.padding}",
  gap: "{list.gap}",
  header: {
    padding: "{list.header.padding}"
  }
};
var i = {
  focusBackground: "{list.option.focus.background}",
  selectedBackground: "{list.option.selected.background}",
  selectedFocusBackground: "{list.option.selected.focus.background}",
  color: "{list.option.color}",
  focusColor: "{list.option.focus.color}",
  selectedColor: "{list.option.selected.color}",
  selectedFocusColor: "{list.option.selected.focus.color}",
  padding: "{list.option.padding}",
  borderRadius: "{list.option.border.radius}"
};
var e = {
  background: "{list.option.group.background}",
  color: "{list.option.group.color}",
  fontWeight: "{list.option.group.font.weight}",
  padding: "{list.option.group.padding}"
};
var f = {
  color: "{form.field.icon.color}"
};
var c = {
  color: "{list.option.color}",
  gutterStart: "-0.5rem",
  gutterEnd: "0.5rem"
};
var a = {
  padding: "{list.option.padding}"
};
var n = {
  root: o,
  dropdown: r,
  overlay: d,
  list: l,
  option: i,
  optionGroup: e,
  clearIcon: f,
  checkmark: c,
  emptyMessage: a
};
export {
  c as checkmark,
  f as clearIcon,
  n as default,
  r as dropdown,
  a as emptyMessage,
  l as list,
  i as option,
  e as optionGroup,
  d as overlay,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_select.js.map
