{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/drawer/index.ts"], "sourcesContent": ["import type { DrawerDesignTokens, DrawerTokenSections } from '@primeuix/themes/types/drawer';\n\nexport const root: DrawerTokenSections.Root = {\n    background: '{overlay.modal.background}',\n    borderColor: '{overlay.modal.border.color}',\n    color: '{overlay.modal.color}',\n    shadow: '{overlay.modal.shadow}'\n};\n\nexport const header: DrawerTokenSections.Header = {\n    padding: '{overlay.modal.padding}'\n};\n\nexport const title: DrawerTokenSections.Title = {\n    fontSize: '1.5rem',\n    fontWeight: '600'\n};\n\nexport const content: DrawerTokenSections.Content = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}'\n};\n\nexport const footer: DrawerTokenSections.Footer = {\n    padding: '{overlay.modal.padding}'\n};\n\nexport default {\n    root,\n    header,\n    title,\n    content,\n    footer\n} satisfies DrawerDesignTokens;\n"], "mappings": ";;;;EAEa,YAAiC;EAC1C,aAAY;EACZ,OAAA;EACA,QAAO;AACP;IACJ,IAAA;EAEa,SAAqC;AAC9C;IACJ,IAAA;EAEa,UAAmC;EAC5C,YAAU;AACV;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,SAAqC;AAC9C;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;AACA;", "names": []}