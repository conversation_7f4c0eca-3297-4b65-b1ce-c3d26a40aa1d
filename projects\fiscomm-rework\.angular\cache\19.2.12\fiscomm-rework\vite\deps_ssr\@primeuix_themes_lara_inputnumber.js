import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/inputnumber/index.mjs
var r = {
  transitionDuration: "{transition.duration}"
};
var o = {
  width: "2.5rem",
  borderRadius: "{form.field.border.radius}",
  verticalPadding: "{form.field.padding.y}"
};
var e = {
  light: {
    button: {
      background: "{surface.100}",
      hoverBackground: "{surface.200}",
      activeBackground: "{surface.300}",
      borderColor: "{form.field.border.color}",
      hoverBorderColor: "{form.field.border.color}",
      activeBorderColor: "{form.field.border.color}",
      color: "{surface.600}",
      hoverColor: "{surface.700}",
      activeColor: "{surface.800}"
    }
  },
  dark: {
    button: {
      background: "{surface.800}",
      hoverBackground: "{surface.700}",
      activeBackground: "{surface.500}",
      borderColor: "{form.field.border.color}",
      hoverBorderColor: "{form.field.border.color}",
      activeBorderColor: "{form.field.border.color}",
      color: "{surface.300}",
      hoverColor: "{surface.200}",
      activeColor: "{surface.100}"
    }
  }
};
var d = {
  root: r,
  button: o,
  colorScheme: e
};
export {
  o as button,
  e as colorScheme,
  d as default,
  r as root
};
//# sourceMappingURL=@primeuix_themes_lara_inputnumber.js.map
