{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/fieldset/index.ts"], "sourcesContent": ["import type { FieldsetDesignTokens, FieldsetTokenSections } from '@primeuix/themes/types/fieldset';\n\nexport const root: FieldsetTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    padding: '0.75rem 1.125rem 1.125rem 1.125rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const legend: FieldsetTokenSections.Legend = {\n    borderRadius: '{content.border.radius}',\n    borderWidth: '1px',\n    borderColor: '{content.border.color}',\n    padding: '0.625rem 0.875rem',\n    gap: '0.5rem',\n    fontWeight: '700',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const toggleIcon: FieldsetTokenSections.ToggleIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}'\n};\n\nexport const content: FieldsetTokenSections.Content = {\n    padding: '0'\n};\n\nexport const colorScheme: FieldsetTokenSections.ColorScheme = {\n    light: {\n        legend: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            color: '{text.color}',\n            hoverColor: '{text.hover.color}'\n        }\n    },\n    dark: {\n        legend: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            color: '{text.color}',\n            hoverColor: '{text.hover.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    legend,\n    toggleIcon,\n    content,\n    colorScheme\n} satisfies FieldsetDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAmC;EAC5C,aAAY;EACZ,cAAa;EACb,OAAA;EACA,SAAO;EACP,oBAAS;AACT;IACJ,IAAA;EAEa,cAAuC;EAChD,aAAc;EACd,aAAa;EACb,SAAA;EACA,KAAA;EACA,YAAK;EACL,WAAY;IACZ,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;AACP;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,iBAAY;MACZ,OAAA;MACA,YAAO;IACP;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,iBAAY;MACZ,OAAA;MACA,YAAO;IACP;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,YAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}