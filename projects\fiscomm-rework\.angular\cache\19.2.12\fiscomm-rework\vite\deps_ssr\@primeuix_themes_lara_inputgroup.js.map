{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inputgroup/index.ts"], "sourcesContent": ["import type { InputGroupDesignTokens, InputGroupTokenSections } from '@primeuix/themes/types/inputgroup';\n\nexport const addon: InputGroupTokenSections.Addon = {\n    borderRadius: '{form.field.border.radius}',\n    padding: '0.625rem 0.5rem',\n    minWidth: '2.75rem'\n};\n\nexport const colorScheme: InputGroupTokenSections.ColorScheme = {\n    light: {\n        addon: {\n            background: '{surface.50}',\n            borderColor: '{form.field.border.color}',\n            color: '{text.muted.color}'\n        }\n    },\n    dark: {\n        addon: {\n            background: '{surface.800}',\n            borderColor: '{form.field.border.color}',\n            color: '{text.muted.color}'\n        }\n    }\n};\n\nexport default {\n    addon,\n    colorScheme\n} satisfies InputGroupDesignTokens;\n"], "mappings": ";;;;;EAEa,cAAuC;EAChD,SAAA;EACA,UAAS;AACT;IACJ,IAAA;EAEa,OAAA;IACT,OAAO;MACH,YAAO;MACH,aAAY;MACZ,OAAA;IACA;EACJ;EACJ,MAAA;IACA,OAAM;MACF,YAAO;MACH,aAAY;MACZ,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,OAAA;EACH,aAAA;AACA;", "names": []}