{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/autocomplete/index.ts"], "sourcesContent": ["import type { AutoCompleteDesignTokens, AutoCompleteTokenSections } from '@primeuix/themes/types/autocomplete';\n\nexport const root: AutoCompleteTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    filledHoverBackground: '{form.field.filled.hover.background}',\n    filledFocusBackground: '{form.field.filled.focus.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    placeholderColor: '{form.field.placeholder.color}',\n    invalidPlaceholderColor: '{form.field.invalid.placeholder.color}',\n    shadow: '{form.field.shadow}',\n    paddingX: '{form.field.padding.x}',\n    paddingY: '{form.field.padding.y}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}'\n};\n\nexport const overlay: AutoCompleteTokenSections.Overlay = {\n    background: '{overlay.select.background}',\n    borderColor: '{overlay.select.border.color}',\n    borderRadius: '{overlay.select.border.radius}',\n    color: '{overlay.select.color}',\n    shadow: '{overlay.select.shadow}'\n};\n\nexport const list: AutoCompleteTokenSections.List = {\n    padding: '{list.padding}',\n    gap: '{list.gap}'\n};\n\nexport const option: AutoCompleteTokenSections.Option = {\n    focusBackground: '{list.option.focus.background}',\n    selectedBackground: '{list.option.selected.background}',\n    selectedFocusBackground: '{list.option.selected.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    selectedColor: '{list.option.selected.color}',\n    selectedFocusColor: '{list.option.selected.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const optionGroup: AutoCompleteTokenSections.OptionGroup = {\n    background: '{list.option.group.background}',\n    color: '{list.option.group.color}',\n    fontWeight: '{list.option.group.font.weight}',\n    padding: '{list.option.group.padding}'\n};\n\nexport const dropdown: AutoCompleteTokenSections.Dropdown = {\n    width: '2.5rem',\n    sm: {\n        width: '2rem'\n    },\n    lg: {\n        width: '3rem'\n    },\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.border.color}',\n    activeBorderColor: '{form.field.border.color}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    }\n};\n\nexport const chip: AutoCompleteTokenSections.Chip = {\n    borderRadius: '{border.radius.sm}'\n};\n\nexport const emptyMessage: AutoCompleteTokenSections.EmptyMessage = {\n    padding: '{list.option.padding}'\n};\n\nexport const colorScheme: AutoCompleteTokenSections.ColorScheme = {\n    light: {\n        chip: {\n            focusBackground: '{surface.200}',\n            focusColor: '{surface.800}'\n        },\n        dropdown: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            activeBackground: '{surface.200}',\n            color: '{surface.600}',\n            hoverColor: '{surface.700}',\n            activeColor: '{surface.800}'\n        }\n    },\n    dark: {\n        chip: {\n            focusBackground: '{surface.700}',\n            focusColor: '{surface.0}'\n        },\n        dropdown: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.600}',\n            color: '{surface.300}',\n            hoverColor: '{surface.200}',\n            activeColor: '{surface.100}'\n        }\n    }\n};\n\nexport default {\n    root,\n    overlay,\n    list,\n    option,\n    optionGroup,\n    dropdown,\n    chip,\n    emptyMessage,\n    colorScheme\n} satisfies AutoCompleteDesignTokens;\n"], "mappings": ";;;;EAEa,YAAuC;EAChD,oBAAY;EACZ,kBAAoB;EACpB,uBAAkB;EAClB,uBAAuB;EACvB,aAAA;EACA,kBAAa;EACb,kBAAkB;EAClB,oBAAkB;EAClB,OAAA;EACA,eAAO;EACP,kBAAe;EACf,yBAAkB;EAClB,QAAA;EACA,UAAQ;EACR,UAAU;EACV,cAAU;EACV,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;AACA;IACJ,IAAA;EAEa,YAA6C;EACtD,aAAY;EACZ,cAAa;EACb,OAAA;EACA,QAAO;AACP;IACJ,IAAA;EAEa,SAAuC;EAChD,KAAA;AACA;IACJ,IAAA;EAEa,iBAA2C;EACpD,oBAAiB;EACjB,yBAAoB;EACpB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,oBAAe;EACf,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,YAAA;EACT,OAAA;EACA,YAAO;EACP,SAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,IAAA;IACI,OAAA;EACA;EACJ,IAAA;IACI,OAAA;EACA;EACJ,aAAA;EACA,kBAAa;EACb,mBAAkB;EAClB,cAAA;EACA,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,cAAuC;AAChD;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,iBAAM;MACF,YAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,iBAAY;MACZ,kBAAiB;MACjB,OAAA;MACA,YAAO;MACP,aAAY;IACZ;EACJ;EACJ,MAAA;IACA,MAAM;MACF,iBAAM;MACF,YAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,iBAAY;MACZ,kBAAiB;MACjB,OAAA;MACA,YAAO;MACP,aAAY;IACZ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,MAAA;EACA,QAAA;EACA,aAAA;EACA,UAAA;EACA,MAAA;EACA,cAAA;EACA,aAAA;AACA;", "names": []}