{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tree/index.ts"], "sourcesContent": ["import type { TreeDesignTokens, TreeTokenSections } from '@primeuix/themes/types/tree';\n\nexport const root: TreeTokenSections.Root = {\n    background: '{content.background}',\n    color: '{content.color}',\n    padding: '1rem',\n    gap: '2px',\n    indent: '1rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const node: TreeTokenSections.Node = {\n    padding: '0.375rem 0.625rem',\n    borderRadius: '{content.border.radius}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    color: '{text.color}',\n    hoverColor: '{text.hover.color}',\n    selectedColor: '{highlight.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    },\n    gap: '0.25rem'\n};\n\nexport const nodeIcon: TreeTokenSections.NodeIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    selectedColor: '{highlight.color}'\n};\n\nexport const nodeToggleButton: TreeTokenSections.NodeToggleButton = {\n    borderRadius: '50%',\n    size: '1.75rem',\n    hoverBackground: '{content.hover.background}',\n    selectedHoverBackground: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    selectedHoverColor: '{primary.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const loadingIcon: TreeTokenSections.LoadingIcon = {\n    size: '2rem'\n};\n\nexport const filter: TreeTokenSections.Filter = {\n    margin: '0 0 0.5rem 0'\n};\n\nexport default {\n    root,\n    node,\n    nodeIcon,\n    nodeToggleButton,\n    loadingIcon,\n    filter\n} satisfies TreeDesignTokens;\n"], "mappings": ";;;;EAEa,YAA+B;EACxC,OAAA;EACA,SAAO;EACP,KAAA;EACA,QAAK;EACL,oBAAQ;AACR;IACJ,IAAA;EAEa,SAA+B;EACxC,cAAS;EACT,iBAAc;EACd,oBAAiB;EACjB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,KAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,eAAY;AACZ;IACJ,IAAA;EAEa,cAAA;EACT,MAAA;EACA,iBAAM;EACN,yBAAiB;EACjB,OAAA;EACA,YAAO;EACP,oBAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,QAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,UAAA;EACA,kBAAA;EACA,aAAA;EACA,QAAA;AACA;", "names": []}