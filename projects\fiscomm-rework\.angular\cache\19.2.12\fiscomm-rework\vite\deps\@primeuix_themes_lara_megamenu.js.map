{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/megamenu/index.ts"], "sourcesContent": ["import type { MegaMenuDesignTokens, MegaMenuTokenSections } from '@primeuix/themes/types/megamenu';\n\nexport const root: MegaMenuTokenSections.Root = {\n    borderColor: 'transparent',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    gap: '0.5rem',\n    verticalOrientation: {\n        padding: '{navigation.list.padding}',\n        gap: '{navigation.list.gap}'\n    },\n    horizontalOrientation: {\n        padding: '0.75rem 1rem',\n        gap: '0.5rem'\n    },\n    transitionDuration: '{transition.duration}'\n};\n\nexport const baseItem: MegaMenuTokenSections.BaseItem = {\n    borderRadius: '{content.border.radius}',\n    padding: '0.75rem 1rem'\n};\n\nexport const item: MegaMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const overlay: MegaMenuTokenSections.Overlay = {\n    padding: '0',\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    shadow: '{overlay.navigation.shadow}',\n    gap: '0.5rem'\n};\n\nexport const submenu: MegaMenuTokenSections.Submenu = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const submenuLabel: MegaMenuTokenSections.SubmenuLabel = {\n    padding: '{navigation.submenu.label.padding}',\n    fontWeight: '{navigation.submenu.label.font.weight}',\n    background: '{navigation.submenu.label.background.}',\n    color: '{navigation.submenu.label.color}'\n};\n\nexport const submenuIcon: MegaMenuTokenSections.SubmenuIcon = {\n    size: '{navigation.submenu.icon.size}',\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}',\n    activeColor: '{navigation.submenu.icon.active.color}'\n};\n\nexport const separator: MegaMenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport const mobileButton: MegaMenuTokenSections.MobileButton = {\n    borderRadius: '50%',\n    size: '2rem',\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    hoverBackground: '{content.hover.background}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: MegaMenuTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.50}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    root,\n    baseItem,\n    item,\n    overlay,\n    submenu,\n    submenuLabel,\n    submenuIcon,\n    separator,\n    mobileButton,\n    colorScheme\n} satisfies MegaMenuDesignTokens;\n"], "mappings": ";;;;EAEa,aAAmC;EAC5C,cAAa;EACb,OAAA;EACA,KAAO;EACP,qBAAK;IACL,SAAA;IACI,KAAA;EACA;EACJ,uBAAA;IACA,SAAA;IACI,KAAA;EACA;EACJ,oBAAA;AACA;IACJ,IAAA;EAEa,cAA2C;EACpD,SAAA;AACA;IACJ,IAAA;EAEa,iBAAmC;EAC5C,kBAAiB;EACjB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,cAAS;EACT,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;IACP,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,SAAA;EACT,YAAS;EACT,aAAY;EACZ,cAAa;EACb,OAAA;EACA,QAAO;EACP,KAAA;AACA;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;AACA;IACJ,IAAA;EAEa,SAAA;EACT,YAAS;EACT,YAAY;EACZ,OAAA;AACA;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;EACN,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,aAA6C;AACtD;IACJ,IAAA;EAEa,cAAA;EACT,MAAA;EACA,OAAM;EACN,YAAO;EACP,iBAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;IACF;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;IACF;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,UAAA;EACA,MAAA;EACA,SAAA;EACA,SAAA;EACA,cAAA;EACA,aAAA;EACA,WAAA;EACA,cAAA;EACA,aAAA;AACA;", "names": []}