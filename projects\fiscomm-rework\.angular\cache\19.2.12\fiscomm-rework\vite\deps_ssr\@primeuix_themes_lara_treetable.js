import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/treetable/index.mjs
var o = {
  transitionDuration: "{transition.duration}"
};
var r = {
  borderColor: "{treetable.border.color}",
  borderWidth: "1px 0 1px 0",
  padding: "0.75rem 1rem"
};
var e = {
  selectedBackground: "{highlight.background}",
  borderColor: "{treetable.border.color}",
  hoverColor: "{content.hover.color}",
  selectedColor: "{highlight.color}",
  gap: "0.5rem",
  padding: "0.75rem 1rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  }
};
var t = {
  fontWeight: "700"
};
var c = {
  background: "{content.background}",
  hoverBackground: "{content.hover.background}",
  selectedBackground: "{highlight.background}",
  color: "{content.color}",
  hoverColor: "{sr.hover.color}",
  selectedColor: "{highlight.color}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  }
};
var l = {
  borderColor: "{treetable.border.color}",
  padding: "0.75rem 1rem",
  gap: "0.5rem"
};
var d = {
  borderColor: "{treetable.border.color}",
  padding: "0.75rem 1rem"
};
var n = {
  fontWeight: "700"
};
var a = {
  borderColor: "{treetable.border.color}",
  borderWidth: "0 0 1px 0",
  padding: "0.75rem 1rem"
};
var i = {
  width: "0.5rem"
};
var s = {
  width: "1px",
  color: "{primary.color}"
};
var g = {
  color: "{text.muted.color}",
  hoverColor: "{text.hover.muted.color}",
  size: "0.875rem"
};
var u = {
  size: "2rem"
};
var h = {
  hoverBackground: "{content.hover.background}",
  selectedHoverBackground: "{content.background}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  selectedHoverColor: "{primary.color}",
  size: "1.75rem",
  borderRadius: "50%",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var f = {
  borderColor: "{content.border.color}",
  borderWidth: "0 0 1px 0"
};
var b = {
  borderColor: "{content.border.color}",
  borderWidth: "0 0 1px 0"
};
var m = {
  light: {
    root: {
      borderColor: "{content.border.color}"
    },
    header: {
      background: "{surface.50}",
      color: "{text.color}"
    },
    headerCell: {
      background: "{surface.50}",
      hoverBackground: "{surface.100}",
      color: "{text.color}"
    },
    footer: {
      background: "{surface.50}",
      color: "{text.color}"
    },
    footerCell: {
      background: "{surface.50}",
      color: "{text.color}"
    },
    bodyCell: {
      selectedBorderColor: "{primary.100}"
    }
  },
  dark: {
    root: {
      borderColor: "{surface.800}"
    },
    header: {
      background: "{surface.800}",
      color: "{text.color}"
    },
    headerCell: {
      background: "{surface.800}",
      hoverBackground: "{surface.700}",
      color: "{text.color}"
    },
    footer: {
      background: "{surface.800}",
      color: "{text.color}"
    },
    footerCell: {
      background: "{surface.800}",
      color: "{text.color}"
    },
    bodyCell: {
      selectedBorderColor: "{primary.900}"
    }
  }
};
var C = {
  root: o,
  header: r,
  headerCell: e,
  columnTitle: t,
  row: c,
  bodyCell: l,
  footerCell: d,
  columnFooter: n,
  footer: a,
  columnResizer: i,
  resizeIndicator: s,
  sortIcon: g,
  loadingIcon: u,
  nodeToggleButton: h,
  paginatorTop: f,
  paginatorBottom: b,
  colorScheme: m
};
export {
  l as bodyCell,
  m as colorScheme,
  n as columnFooter,
  i as columnResizer,
  t as columnTitle,
  C as default,
  a as footer,
  d as footerCell,
  r as header,
  e as headerCell,
  u as loadingIcon,
  h as nodeToggleButton,
  b as paginatorBottom,
  f as paginatorTop,
  s as resizeIndicator,
  o as root,
  c as row,
  g as sortIcon
};
//# sourceMappingURL=@primeuix_themes_lara_treetable.js.map
