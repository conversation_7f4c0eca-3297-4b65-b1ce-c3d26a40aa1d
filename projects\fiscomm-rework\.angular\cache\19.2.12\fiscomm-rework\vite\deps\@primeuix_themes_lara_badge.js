import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/badge/index.mjs
var r = {
  borderRadius: "{border.radius.md}",
  padding: "0 0.5rem",
  fontSize: "0.75rem",
  fontWeight: "700",
  minWidth: "1.5rem",
  height: "1.5rem"
};
var o = {
  size: "0.5rem"
};
var e = {
  fontSize: "0.625rem",
  minWidth: "1.25rem",
  height: "1.25rem"
};
var c = {
  fontSize: "0.875rem",
  minWidth: "1.75rem",
  height: "1.75rem"
};
var a = {
  fontSize: "1rem",
  minWidth: "2rem",
  height: "2rem"
};
var n = {
  light: {
    primary: {
      background: "{primary.color}",
      color: "{primary.contrast.color}"
    },
    secondary: {
      background: "{surface.100}",
      color: "{surface.600}"
    },
    success: {
      background: "{green.500}",
      color: "{surface.0}"
    },
    info: {
      background: "{sky.500}",
      color: "{surface.0}"
    },
    warn: {
      background: "{orange.500}",
      color: "{surface.0}"
    },
    danger: {
      background: "{red.500}",
      color: "{surface.0}"
    },
    contrast: {
      background: "{surface.950}",
      color: "{surface.0}"
    }
  },
  dark: {
    primary: {
      background: "{primary.color}",
      color: "{primary.contrast.color}"
    },
    secondary: {
      background: "{surface.800}",
      color: "{surface.300}"
    },
    success: {
      background: "{green.400}",
      color: "{green.950}"
    },
    info: {
      background: "{sky.400}",
      color: "{sky.950}"
    },
    warn: {
      background: "{orange.400}",
      color: "{orange.950}"
    },
    danger: {
      background: "{red.400}",
      color: "{red.950}"
    },
    contrast: {
      background: "{surface.0}",
      color: "{surface.950}"
    }
  }
};
var d = {
  root: r,
  dot: o,
  sm: e,
  lg: c,
  xl: a,
  colorScheme: n
};
export {
  n as colorScheme,
  d as default,
  o as dot,
  c as lg,
  r as root,
  e as sm,
  a as xl
};
//# sourceMappingURL=@primeuix_themes_lara_badge.js.map
