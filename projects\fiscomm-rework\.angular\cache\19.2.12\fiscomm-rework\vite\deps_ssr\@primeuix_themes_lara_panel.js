import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/panel/index.mjs
var r = {
  background: "{content.background}",
  borderColor: "{content.border.color}",
  borderRadius: "{content.border.radius}",
  color: "{content.color}"
};
var o = {
  borderWidth: "0 0 1px 0",
  borderColor: "{content.border.color}",
  padding: "1.125rem",
  borderRadius: "5px 5px 0 0"
};
var e = {
  padding: "0.25rem 1.125rem"
};
var d = {
  fontWeight: "700"
};
var t = {
  padding: "1.125rem"
};
var a = {
  padding: "1.125rem"
};
var c = {
  light: {
    header: {
      background: "{surface.50}",
      color: "{text.color}"
    }
  },
  dark: {
    header: {
      background: "{surface.800}",
      color: "{text.color}"
    }
  }
};
var n = {
  root: r,
  header: o,
  toggleableHeader: e,
  title: d,
  content: t,
  footer: a,
  colorScheme: c
};
export {
  c as colorScheme,
  t as content,
  n as default,
  a as footer,
  o as header,
  r as root,
  d as title,
  e as toggleableHeader
};
//# sourceMappingURL=@primeuix_themes_lara_panel.js.map
