{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/timeline/index.ts"], "sourcesContent": ["import type { TimelineDesignTokens, TimelineTokenSections } from '@primeuix/themes/types/timeline';\n\nexport const event: TimelineTokenSections.Event = {\n    minHeight: '5rem'\n};\n\nexport const horizontal: TimelineTokenSections.Horizontal = {\n    eventContent: {\n        padding: '1rem 0'\n    }\n};\n\nexport const vertical: TimelineTokenSections.Vertical = {\n    eventContent: {\n        padding: '0 1rem'\n    }\n};\n\nexport const eventMarker: TimelineTokenSections.EventMarker = {\n    size: '1.125rem',\n    borderRadius: '50%',\n    borderWidth: '2px',\n    background: '{content.background}',\n    borderColor: '{primary.color}',\n    content: {\n        borderRadius: '50%',\n        size: '0.375rem',\n        background: 'transparent',\n        insetShadow: 'none'\n    }\n};\n\nexport const eventConnector: TimelineTokenSections.EventConnector = {\n    color: '{content.border.color}',\n    size: '2px'\n};\n\nexport default {\n    event,\n    horizontal,\n    vertical,\n    eventMarker,\n    eventConnector\n} satisfies TimelineDesignTokens;\n"], "mappings": ";;;;;EAEa,WAAqC;AAC9C;IACJ,IAAA;EAEa,cAA+C;IACxD,SAAA;EACI;AACJ;IACJ,IAAA;EAEa,cAA2C;IACpD,SAAA;EACI;AACJ;IACJ,IAAA;EAEa,MAAA;EACT,cAAM;EACN,aAAc;EACd,YAAa;EACb,aAAY;EACZ,SAAA;IACA,cAAS;IACL,MAAA;IACA,YAAM;IACN,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,OAAA;EACT,MAAO;AACP;IACJ,IAAA;EAEO,OAAA;EACH,YAAA;EACA,UAAA;EACA,aAAA;EACA,gBAAA;AACA;", "names": []}