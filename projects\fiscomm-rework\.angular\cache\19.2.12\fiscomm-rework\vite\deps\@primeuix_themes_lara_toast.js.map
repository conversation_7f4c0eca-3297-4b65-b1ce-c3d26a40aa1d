{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/toast/index.ts"], "sourcesContent": ["import type { ToastDesignTokens, ToastTokenSections } from '@primeuix/themes/types/toast';\n\nexport const root: ToastTokenSections.Root = {\n    width: '25rem',\n    borderRadius: '{content.border.radius}',\n    borderWidth: '0 0 0 6px',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const icon: ToastTokenSections.Icon = {\n    size: '1.25rem'\n};\n\nexport const content: ToastTokenSections.Content = {\n    padding: '{overlay.popover.padding}',\n    gap: '0.5rem'\n};\n\nexport const text: ToastTokenSections.Text = {\n    gap: '0.5rem'\n};\n\nexport const summary: ToastTokenSections.Summary = {\n    fontWeight: '500',\n    fontSize: '1rem'\n};\n\nexport const detail: ToastTokenSections.Detail = {\n    fontWeight: '500',\n    fontSize: '0.875rem'\n};\n\nexport const closeButton: ToastTokenSections.CloseButton = {\n    width: '2rem',\n    height: '2rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        offset: '{focus.ring.offset}'\n    }\n};\n\nexport const closeIcon: ToastTokenSections.CloseIcon = {\n    size: '1rem'\n};\n\nexport const colorScheme: ToastTokenSections.ColorScheme = {\n    light: {\n        root: {\n            blur: '1.5px'\n        },\n        info: {\n            background: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            borderColor: '{blue.500}',\n            color: '{blue.600}',\n            detailColor: '{surface.700}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{blue.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {blue.200}'\n                }\n            }\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            borderColor: '{green.500}',\n            color: '{green.600}',\n            detailColor: '{surface.700}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{green.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {green.200}'\n                }\n            }\n        },\n        warn: {\n            background: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            borderColor: '{yellow.500}',\n            color: '{yellow.600}',\n            detailColor: '{surface.700}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{yellow.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {yellow.200}'\n                }\n            }\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            borderColor: '{red.500}',\n            color: '{red.600}',\n            detailColor: '{surface.700}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{red.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {red.200}'\n                }\n            }\n        },\n        secondary: {\n            background: '{surface.100}',\n            borderColor: '{surface.500}',\n            color: '{surface.600}',\n            detailColor: '{surface.700}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{surface.200}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {surface.200}'\n                }\n            }\n        },\n        contrast: {\n            background: '{surface.900}',\n            borderColor: '{primary.color}',\n            color: '{surface.50}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{surface.800}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {surface.400}'\n                }\n            }\n        }\n    },\n    dark: {\n        root: {\n            blur: '10px'\n        },\n        info: {\n            background: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {blue.700}, transparent 64%)',\n            color: '{blue.500}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {blue.500}, transparent 80%)'\n                }\n            }\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {green.700}, transparent 64%)',\n            color: '{green.500}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {green.500}, transparent 80%)'\n                }\n            }\n        },\n        warn: {\n            background: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {yellow.700}, transparent 64%)',\n            color: '{yellow.500}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {yellow.500}, transparent 80%)'\n                }\n            }\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {red.700}, transparent 64%)',\n            color: '{red.500}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {red.500}, transparent 80%)'\n                }\n            }\n        },\n        secondary: {\n            background: '{surface.800}',\n            borderColor: '{surface.700}',\n            color: '{surface.300}',\n            detailColor: '{surface.0}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{surface.700}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {surface.300}, transparent 80%)'\n                }\n            }\n        },\n        contrast: {\n            background: '{surface.0}',\n            borderColor: '{surface.100}',\n            color: '{surface.950}',\n            detailColor: '{surface.950}',\n            shadow: '{overlay.popover.shadow}',\n            closeButton: {\n                hoverBackground: '{surface.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {surface.950}, transparent 80%)'\n                }\n            }\n        }\n    }\n};\n\nexport default {\n    root,\n    icon,\n    content,\n    text,\n    summary,\n    detail,\n    closeButton,\n    closeIcon,\n    colorScheme\n} satisfies ToastDesignTokens;\n"], "mappings": ";;;;EAEa,OAAgC;EACzC,cAAO;EACP,aAAc;EACd,oBAAa;AACb;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;AACA;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,YAAsC;EAC/C,UAAY;AACZ;IACJ,IAAA;EAEa,YAAoC;EAC7C,UAAY;AACZ;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,cAAQ;EACR,WAAA;IACA,OAAW;IACP,OAAO;IACP,QAAO;EACP;AACJ;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,MAAM;IACF;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;EACJ;EACJ,MAAA;IACA,MAAM;MACF,MAAM;IACF;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,aAAO;MACP,QAAA;MACA,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,SAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,aAAA;EACA,WAAA;EACA,aAAA;AACA;", "names": []}