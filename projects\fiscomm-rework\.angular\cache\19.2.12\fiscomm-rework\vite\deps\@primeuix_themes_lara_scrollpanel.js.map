{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/scrollpanel/index.ts"], "sourcesContent": ["import type { ScrollPanelDesignTokens, ScrollPanelTokenSections } from '@primeuix/themes/types/scrollpanel';\n\nexport const root: ScrollPanelTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const bar: ScrollPanelTokenSections.Bar = {\n    size: '9px',\n    borderRadius: '{border.radius.sm}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: ScrollPanelTokenSections.ColorScheme = {\n    light: {\n        bar: {\n            background: '{surface.200}'\n        }\n    },\n    dark: {\n        bar: {\n            background: '{surface.700}'\n        }\n    }\n};\n\nexport default {\n    root,\n    bar,\n    colorScheme\n} satisfies ScrollPanelDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAsC;AAC/C;IACJ,IAAA;EAEa,MAAoC;EAC7C,cAAM;EACN,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,KAAO;MACH,YAAK;IACD;EACJ;EACJ,MAAA;IACA,KAAM;MACF,YAAK;IACD;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,KAAA;EACA,aAAA;AACA;", "names": []}