{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/treetable/index.ts"], "sourcesContent": ["import type { TreeTableDesignTokens, TreeTableTokenSections } from '@primeuix/themes/types/treetable';\n\nexport const root: TreeTableTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const header: TreeTableTokenSections.Header = {\n    borderColor: '{treetable.border.color}',\n    borderWidth: '1px 0 1px 0',\n    padding: '0.75rem 1rem'\n};\n\nexport const headerCell: TreeTableTokenSections.HeaderCell = {\n    selectedBackground: '{highlight.background}',\n    borderColor: '{treetable.border.color}',\n    hoverColor: '{content.hover.color}',\n    selectedColor: '{highlight.color}',\n    gap: '0.5rem',\n    padding: '0.75rem 1rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const columnTitle: TreeTableTokenSections.ColumnTitle = {\n    fontWeight: '700'\n};\n\nexport const row: TreeTableTokenSections.Row = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    color: '{content.color}',\n    hoverColor: '{sr.hover.color}',\n    selectedColor: '{highlight.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const bodyCell: TreeTableTokenSections.BodyCell = {\n    borderColor: '{treetable.border.color}',\n    padding: '0.75rem 1rem',\n    gap: '0.5rem'\n};\n\nexport const footerCell: TreeTableTokenSections.FooterCell = {\n    borderColor: '{treetable.border.color}',\n    padding: '0.75rem 1rem'\n};\n\nexport const columnFooter: TreeTableTokenSections.ColumnFooter = {\n    fontWeight: '700'\n};\n\nexport const footer: TreeTableTokenSections.Footer = {\n    borderColor: '{treetable.border.color}',\n    borderWidth: '0 0 1px 0',\n    padding: '0.75rem 1rem'\n};\n\nexport const columnResizer: TreeTableTokenSections.ColumnResizer = {\n    width: '0.5rem'\n};\n\nexport const resizeIndicator: TreeTableTokenSections.ResizeIndicator = {\n    width: '1px',\n    color: '{primary.color}'\n};\n\nexport const sortIcon: TreeTableTokenSections.SortIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    size: '0.875rem'\n};\n\nexport const loadingIcon: TreeTableTokenSections.LoadingIcon = {\n    size: '2rem'\n};\n\nexport const nodeToggleButton: TreeTableTokenSections.NodeToggleButton = {\n    hoverBackground: '{content.hover.background}',\n    selectedHoverBackground: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    selectedHoverColor: '{primary.color}',\n    size: '1.75rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const paginatorTop: TreeTableTokenSections.PaginatorTop = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const paginatorBottom: TreeTableTokenSections.PaginatorBottom = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const colorScheme: TreeTableTokenSections.ColorScheme = {\n    light: {\n        root: {\n            borderColor: '{content.border.color}'\n        },\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.100}'\n        }\n    },\n    dark: {\n        root: {\n            borderColor: '{surface.800}'\n        },\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    headerCell,\n    columnTitle,\n    row,\n    bodyCell,\n    footerCell,\n    columnFooter,\n    footer,\n    columnResizer,\n    resizeIndicator,\n    sortIcon,\n    loadingIcon,\n    nodeToggleButton,\n    paginatorTop,\n    paginatorBottom,\n    colorScheme\n} satisfies TreeTableDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAoC;AAC7C;IACJ,IAAA;EAEa,aAAwC;EACjD,aAAa;EACb,SAAA;AACA;IACJ,IAAA;EAEa,oBAAgD;EACzD,aAAA;EACA,YAAa;EACb,eAAY;EACZ,KAAA;EACA,SAAK;EACL,WAAS;IACT,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,YAAA;AACT;IACJ,IAAA;EAEa,YAAkC;EAC3C,iBAAY;EACZ,oBAAiB;EACjB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,aAA4C;EACrD,SAAA;EACA,KAAA;AACA;IACJ,IAAA;EAEa,aAAgD;EACzD,SAAA;AACA;IACJ,IAAA;EAEa,YAAA;AACT;IACJ,IAAA;EAEa,aAAwC;EACjD,aAAa;EACb,SAAA;AACA;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEa,OAAA;EACT,OAAO;AACP;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,MAAA;AACA;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,iBAAA;EACT,yBAAiB;EACjB,OAAA;EACA,YAAO;EACP,oBAAY;EACZ,MAAA;EACA,cAAM;EACN,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,aAAM;IACF;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,iBAAY;MACZ,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,OAAA;IACA;IACJ,UAAA;MACA,qBAAU;IACN;EACJ;EACJ,MAAA;IACA,MAAM;MACF,aAAM;IACF;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,iBAAY;MACZ,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,OAAA;IACA;IACJ,UAAA;MACA,qBAAU;IACN;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,YAAA;EACA,aAAA;EACA,KAAA;EACA,UAAA;EACA,YAAA;EACA,cAAA;EACA,QAAA;EACA,eAAA;EACA,iBAAA;EACA,UAAA;EACA,aAAA;EACA,kBAAA;EACA,cAAA;EACA,iBAAA;EACA,aAAA;AACA;", "names": []}