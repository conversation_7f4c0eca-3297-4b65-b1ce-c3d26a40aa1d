import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/multiselect/index.mjs
var o = {
  background: "{form.field.background}",
  disabledBackground: "{form.field.disabled.background}",
  filledBackground: "{form.field.filled.background}",
  filledHoverBackground: "{form.field.filled.hover.background}",
  filledFocusBackground: "{form.field.filled.focus.background}",
  borderColor: "{form.field.border.color}",
  hoverBorderColor: "{form.field.hover.border.color}",
  focusBorderColor: "{form.field.focus.border.color}",
  invalidBorderColor: "{form.field.invalid.border.color}",
  color: "{form.field.color}",
  disabledColor: "{form.field.disabled.color}",
  placeholderColor: "{form.field.placeholder.color}",
  invalidPlaceholderColor: "{form.field.invalid.placeholder.color}",
  shadow: "{form.field.shadow}",
  paddingX: "{form.field.padding.x}",
  paddingY: "{form.field.padding.y}",
  borderRadius: "{form.field.border.radius}",
  focusRing: {
    width: "{form.field.focus.ring.width}",
    style: "{form.field.focus.ring.style}",
    color: "{form.field.focus.ring.color}",
    offset: "{form.field.focus.ring.offset}",
    shadow: "{form.field.focus.ring.shadow}"
  },
  transitionDuration: "{form.field.transition.duration}",
  sm: {
    fontSize: "{form.field.sm.font.size}",
    paddingX: "{form.field.sm.padding.x}",
    paddingY: "{form.field.sm.padding.y}"
  },
  lg: {
    fontSize: "{form.field.lg.font.size}",
    paddingX: "{form.field.lg.padding.x}",
    paddingY: "{form.field.lg.padding.y}"
  }
};
var d = {
  width: "2.5rem",
  color: "{form.field.icon.color}"
};
var r = {
  background: "{overlay.select.background}",
  borderColor: "{overlay.select.border.color}",
  borderRadius: "{overlay.select.border.radius}",
  color: "{overlay.select.color}",
  shadow: "{overlay.select.shadow}"
};
var l = {
  padding: "{list.padding}",
  gap: "{list.gap}",
  header: {
    padding: "{list.header.padding}"
  }
};
var i = {
  focusBackground: "{list.option.focus.background}",
  selectedBackground: "{list.option.selected.background}",
  selectedFocusBackground: "{list.option.selected.focus.background}",
  color: "{list.option.color}",
  focusColor: "{list.option.focus.color}",
  selectedColor: "{list.option.selected.color}",
  selectedFocusColor: "{list.option.selected.focus.color}",
  padding: "{list.option.padding}",
  borderRadius: "{list.option.border.radius}",
  gap: "0.5rem"
};
var e = {
  background: "{list.option.group.background}",
  color: "{list.option.group.color}",
  fontWeight: "{list.option.group.font.weight}",
  padding: "{list.option.group.padding}"
};
var f = {
  color: "{form.field.icon.color}"
};
var a = {
  borderRadius: "{border.radius.sm}"
};
var c = {
  padding: "{list.option.padding}"
};
var n = {
  root: o,
  dropdown: d,
  overlay: r,
  list: l,
  option: i,
  optionGroup: e,
  chip: a,
  clearIcon: f,
  emptyMessage: c
};
export {
  a as chip,
  f as clearIcon,
  n as default,
  d as dropdown,
  c as emptyMessage,
  l as list,
  i as option,
  e as optionGroup,
  r as overlay,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_multiselect.js.map
