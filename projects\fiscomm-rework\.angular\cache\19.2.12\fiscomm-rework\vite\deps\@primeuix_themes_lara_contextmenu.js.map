{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/contextmenu/index.ts"], "sourcesContent": ["import type { ContextMenuDesignTokens, ContextMenuTokenSections } from '@primeuix/themes/types/contextmenu';\n\nexport const root: ContextMenuTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const list: ContextMenuTokenSections.List = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const item: ContextMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const submenu: ContextMenuTokenSections.Submenu = {\n    mobileIndent: '1.25rem'\n};\n\nexport const submenuIcon: ContextMenuTokenSections.SubmenuIcon = {\n    size: '{navigation.submenu.icon.size}',\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}',\n    activeColor: '{navigation.submenu.icon.active.color}'\n};\n\nexport const separator: ContextMenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport default {\n    root,\n    list,\n    item,\n    submenu,\n    submenuIcon,\n    separator\n} satisfies ContextMenuDesignTokens;\n"], "mappings": ";;;;EAEa,YAAsC;EAC/C,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;EACA,oBAAQ;AACR;IACJ,IAAA;EAEa,SAAsC;EAC/C,KAAA;AACA;IACJ,IAAA;EAEa,iBAAsC;EAC/C,kBAAiB;EACjB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,cAAS;EACT,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;IACP,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,cAA4C;AACrD;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;EACN,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,aAAgD;AACzD;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,MAAA;EACA,SAAA;EACA,aAAA;EACA,WAAA;AACA;", "names": []}