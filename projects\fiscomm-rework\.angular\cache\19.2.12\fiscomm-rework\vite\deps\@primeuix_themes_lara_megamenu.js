import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/megamenu/index.mjs
var o = {
  borderColor: "transparent",
  borderRadius: "{content.border.radius}",
  color: "{content.color}",
  gap: "0.5rem",
  verticalOrientation: {
    padding: "{navigation.list.padding}",
    gap: "{navigation.list.gap}"
  },
  horizontalOrientation: {
    padding: "0.75rem 1rem",
    gap: "0.5rem"
  },
  transitionDuration: "{transition.duration}"
};
var n = {
  borderRadius: "{content.border.radius}",
  padding: "0.75rem 1rem"
};
var a = {
  focusBackground: "{navigation.item.focus.background}",
  activeBackground: "{navigation.item.active.background}",
  color: "{navigation.item.color}",
  focusColor: "{navigation.item.focus.color}",
  activeColor: "{navigation.item.active.color}",
  padding: "{navigation.item.padding}",
  borderRadius: "{navigation.item.border.radius}",
  gap: "{navigation.item.gap}",
  icon: {
    color: "{navigation.item.icon.color}",
    focusColor: "{navigation.item.icon.focus.color}",
    activeColor: "{navigation.item.icon.active.color}"
  }
};
var i = {
  padding: "0",
  background: "{content.background}",
  borderColor: "{content.border.color}",
  borderRadius: "{content.border.radius}",
  color: "{content.color}",
  shadow: "{overlay.navigation.shadow}",
  gap: "0.5rem"
};
var r = {
  padding: "{navigation.list.padding}",
  gap: "{navigation.list.gap}"
};
var t = {
  padding: "{navigation.submenu.label.padding}",
  fontWeight: "{navigation.submenu.label.font.weight}",
  background: "{navigation.submenu.label.background.}",
  color: "{navigation.submenu.label.color}"
};
var e = {
  size: "{navigation.submenu.icon.size}",
  color: "{navigation.submenu.icon.color}",
  focusColor: "{navigation.submenu.icon.focus.color}",
  activeColor: "{navigation.submenu.icon.active.color}"
};
var c = {
  borderColor: "{content.border.color}"
};
var d = {
  borderRadius: "50%",
  size: "2rem",
  color: "{text.muted.color}",
  hoverColor: "{text.hover.muted.color}",
  hoverBackground: "{content.hover.background}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var g = {
  light: {
    root: {
      background: "{surface.50}"
    }
  },
  dark: {
    root: {
      background: "{surface.800}"
    }
  }
};
var u = {
  root: o,
  baseItem: n,
  item: a,
  overlay: i,
  submenu: r,
  submenuLabel: t,
  submenuIcon: e,
  separator: c,
  mobileButton: d,
  colorScheme: g
};
export {
  n as baseItem,
  g as colorScheme,
  u as default,
  a as item,
  d as mobileButton,
  i as overlay,
  o as root,
  c as separator,
  r as submenu,
  e as submenuIcon,
  t as submenuLabel
};
//# sourceMappingURL=@primeuix_themes_lara_megamenu.js.map
