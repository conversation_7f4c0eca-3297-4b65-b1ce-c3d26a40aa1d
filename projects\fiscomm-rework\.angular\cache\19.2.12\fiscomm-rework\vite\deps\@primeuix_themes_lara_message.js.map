{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/message/index.ts"], "sourcesContent": ["import type { MessageDesignTokens, MessageTokenSections } from '@primeuix/themes/types/message';\n\nexport const root: MessageTokenSections.Root = {\n    borderRadius: '{content.border.radius}',\n    borderWidth: '1px',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const content: MessageTokenSections.Content = {\n    padding: '0.75rem 1rem',\n    gap: '0.5rem',\n    sm: {\n        padding: '0.5rem 0.625rem'\n    },\n    lg: {\n        padding: '0.75rem 0.875rem'\n    }\n};\n\nexport const text: MessageTokenSections.Text = {\n    fontSize: '1rem',\n    fontWeight: '500',\n    sm: {\n        fontSize: '0.875rem'\n    },\n    lg: {\n        fontSize: '1.125rem'\n    }\n};\n\nexport const icon: MessageTokenSections.Icon = {\n    size: '1.25rem',\n    sm: {\n        size: '1rem'\n    },\n    lg: {\n        size: '1.5rem'\n    }\n};\n\nexport const closeButton: MessageTokenSections.CloseButton = {\n    width: '2rem',\n    height: '2rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        offset: '{focus.ring.offset}'\n    }\n};\n\nexport const closeIcon: MessageTokenSections.CloseIcon = {\n    size: '1rem',\n    sm: {\n        size: '0.875rem'\n    },\n    lg: {\n        size: '1.125rem'\n    }\n};\n\nexport const outlined: MessageTokenSections.Outlined = {\n    root: {\n        borderWidth: '1px'\n    }\n};\n\nexport const simple: MessageTokenSections.Simple = {\n    content: {\n        padding: '0'\n    }\n};\n\nexport const colorScheme: MessageTokenSections.ColorScheme = {\n    light: {\n        info: {\n            background: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            borderColor: 'transparent',\n            color: '{blue.600}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{blue.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {blue.200}'\n                }\n            },\n            outlined: {\n                color: '{blue.600}',\n                borderColor: '{blue.600}'\n            },\n            simple: {\n                color: '{blue.600}'\n            }\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            borderColor: 'transparent',\n            color: '{green.600}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{green.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {green.200}'\n                }\n            },\n            outlined: {\n                color: '{green.600}',\n                borderColor: '{green.600}'\n            },\n            simple: {\n                color: '{green.600}'\n            }\n        },\n        warn: {\n            background: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            borderColor: 'transparent',\n            color: '{yellow.600}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{yellow.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {yellow.200}'\n                }\n            },\n            outlined: {\n                color: '{yellow.600}',\n                borderColor: '{yellow.600}'\n            },\n            simple: {\n                color: '{yellow.600}'\n            }\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            borderColor: 'transparent',\n            color: '{red.600}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{red.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {red.200}'\n                }\n            },\n            outlined: {\n                color: '{red.600}',\n                borderColor: '{red.600}'\n            },\n            simple: {\n                color: '{red.600}'\n            }\n        },\n        secondary: {\n            background: '{surface.100}',\n            borderColor: 'transparent',\n            color: '{surface.600}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{surface.200}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {surface.200}'\n                }\n            },\n            outlined: {\n                color: '{surface.500}',\n                borderColor: '{surface.500}'\n            },\n            simple: {\n                color: '{surface.500}'\n            }\n        },\n        contrast: {\n            background: '{surface.900}',\n            borderColor: 'transparent',\n            color: '{surface.50}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{surface.800}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem {surface.400}'\n                }\n            },\n            outlined: {\n                color: '{surface.900}',\n                borderColor: '{surface.900}'\n            },\n            simple: {\n                color: '{surface.900}'\n            }\n        }\n    },\n    dark: {\n        info: {\n            background: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            borderColor: 'transparent',\n            color: '{blue.500}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {blue.500}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{blue.500}',\n                borderColor: '{blue.500}'\n            },\n            simple: {\n                color: '{blue.500}'\n            }\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            borderColor: 'transparent',\n            color: '{green.500}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {green.500}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{green.500}',\n                borderColor: '{green.500}'\n            },\n            simple: {\n                color: '{green.500}'\n            }\n        },\n        warn: {\n            background: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            borderColor: 'transparent',\n            color: '{yellow.500}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {yellow.500}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{yellow.500}',\n                borderColor: '{yellow.500}'\n            },\n            simple: {\n                color: '{yellow.500}'\n            }\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            borderColor: 'transparent',\n            color: '{red.500}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: 'rgba(255, 255, 255, 0.05)',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {red.500}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{red.500}',\n                borderColor: '{red.500}'\n            },\n            simple: {\n                color: '{red.500}'\n            }\n        },\n        secondary: {\n            background: '{surface.800}',\n            borderColor: 'transparent',\n            color: '{surface.300}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{surface.700}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {surface.300}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{surface.400}',\n                borderColor: '{surface.400}'\n            },\n            simple: {\n                color: '{surface.400}'\n            }\n        },\n        contrast: {\n            background: '{surface.0}',\n            borderColor: 'transparent',\n            color: '{surface.950}',\n            shadow: 'none',\n            closeButton: {\n                hoverBackground: '{surface.100}',\n                focusRing: {\n                    color: '{focus.ring.color}',\n                    shadow: '0 0 0 0.2rem color-mix(in srgb, {surface.950}, transparent 80%)'\n                }\n            },\n            outlined: {\n                color: '{surface.0}',\n                borderColor: '{surface.0}'\n            },\n            simple: {\n                color: '{surface.0}'\n            }\n        }\n    }\n};\n\nexport default {\n    root,\n    content,\n    text,\n    icon,\n    closeButton,\n    closeIcon,\n    outlined,\n    simple,\n    colorScheme\n} satisfies MessageDesignTokens;\n"], "mappings": ";;;;EAEa,cAAkC;EAC3C,aAAc;EACd,oBAAa;AACb;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;EACA,IAAK;IACD,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,UAAkC;EAC3C,YAAU;EACV,IAAA;IACI,UAAA;EACA;EACJ,IAAA;IACI,UAAA;EACA;AACJ;IACJ,IAAA;EAEa,MAAA;EACT,IAAM;IACF,MAAA;EACA;EACJ,IAAA;IACI,MAAA;EACA;AACJ;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,cAAQ;EACR,WAAA;IACA,OAAW;IACP,OAAO;IACP,QAAO;EACP;AACJ;IACJ,IAAA;EAEa,MAAA;EACT,IAAM;IACF,MAAA;EACA;EACJ,IAAA;IACI,MAAA;EACA;AACJ;IACJ,IAAA;EAEa,MAAA;IACT,aAAM;EACF;AACJ;IACJ,IAAA;EAEa,SAAsC;IAC/C,SAAS;EACL;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,SAAA;MACA,YAAS;MACL,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,MAAA;MACA,YAAM;MACF,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,OAAA;MACA,YAAO;MACH,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,WAAA;MACA,YAAW;MACP,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;IACJ,UAAA;MACA,YAAU;MACN,aAAY;MACZ,OAAA;MACA,QAAO;MACP,aAAQ;QACR,iBAAa;QACT,WAAA;UACA,OAAW;UACP,QAAO;QACP;MACJ;MACJ,UAAA;QACA,OAAU;QACN,aAAO;MACP;MACJ,QAAA;QACA,OAAQ;MACJ;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,MAAA;EACA,MAAA;EACA,aAAA;EACA,WAAA;EACA,UAAA;EACA,QAAA;EACA,aAAA;AACA;", "names": []}