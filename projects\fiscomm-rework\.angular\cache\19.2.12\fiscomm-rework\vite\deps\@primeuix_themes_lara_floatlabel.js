import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/floatlabel/index.mjs
var o = {
  color: "{form.field.float.label.color}",
  focusColor: "{form.field.float.label.focus.color}",
  activeColor: "{form.field.float.label.active.color}",
  invalidColor: "{form.field.float.label.invalid.color}",
  transitionDuration: "0.2s",
  positionX: "{form.field.padding.x}",
  positionY: "{form.field.padding.y}",
  fontWeight: "500",
  active: {
    fontSize: "0.75rem",
    fontWeight: "400"
  }
};
var i = {
  active: {
    top: "-1.375rem"
  }
};
var r = {
  input: {
    paddingTop: "1.875rem",
    paddingBottom: "{form.field.padding.y}"
  },
  active: {
    top: "{form.field.padding.y}"
  }
};
var a = {
  borderRadius: "{border.radius.xs}",
  active: {
    background: "{form.field.background}",
    padding: "0 0.125rem"
  }
};
var d = {
  root: o,
  over: i,
  in: r,
  on: a
};
export {
  d as default,
  r as inside,
  a as on,
  i as over,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_floatlabel.js.map
