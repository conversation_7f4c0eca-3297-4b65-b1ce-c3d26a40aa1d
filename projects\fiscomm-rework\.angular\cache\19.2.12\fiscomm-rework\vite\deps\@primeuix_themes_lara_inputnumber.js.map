{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inputnumber/index.ts"], "sourcesContent": ["import type { InputNumberDesignTokens, InputNumberTokenSections } from '@primeuix/themes/types/inputnumber';\n\nexport const root: InputNumberTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const button: InputNumberTokenSections.Button = {\n    width: '2.5rem',\n    borderRadius: '{form.field.border.radius}',\n    verticalPadding: '{form.field.padding.y}'\n};\n\nexport const colorScheme: InputNumberTokenSections.ColorScheme = {\n    light: {\n        button: {\n            background: '{surface.100}',\n            hoverBackground: '{surface.200}',\n            activeBackground: '{surface.300}',\n            borderColor: '{form.field.border.color}',\n            hoverBorderColor: '{form.field.border.color}',\n            activeBorderColor: '{form.field.border.color}',\n            color: '{surface.600}',\n            hoverColor: '{surface.700}',\n            activeColor: '{surface.800}'\n        }\n    },\n    dark: {\n        button: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.500}',\n            borderColor: '{form.field.border.color}',\n            hoverBorderColor: '{form.field.border.color}',\n            activeBorderColor: '{form.field.border.color}',\n            color: '{surface.300}',\n            hoverColor: '{surface.200}',\n            activeColor: '{surface.100}'\n        }\n    }\n};\n\nexport default {\n    root,\n    button,\n    colorScheme\n} satisfies InputNumberDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAsC;AAC/C;IACJ,IAAA;EAEa,OAAA;EACT,cAAO;EACP,iBAAc;AACd;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,iBAAY;MACZ,kBAAiB;MACjB,aAAA;MACA,kBAAa;MACb,mBAAkB;MAClB,OAAA;MACA,YAAO;MACP,aAAY;IACZ;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,iBAAY;MACZ,kBAAiB;MACjB,aAAA;MACA,kBAAa;MACb,mBAAkB;MAClB,OAAA;MACA,YAAO;MACP,aAAY;IACZ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,aAAA;AACA;", "names": []}