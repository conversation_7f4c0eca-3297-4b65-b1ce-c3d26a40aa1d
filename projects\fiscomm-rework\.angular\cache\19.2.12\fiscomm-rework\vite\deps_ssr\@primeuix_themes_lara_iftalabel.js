import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/iftalabel/index.mjs
var o = {
  color: "{form.field.float.label.color}",
  focusColor: "{form.field.float.label.focus.color}",
  invalidColor: "{form.field.float.label.invalid.color}",
  transitionDuration: "0.2s",
  positionX: "{form.field.padding.x}",
  top: "{form.field.padding.y}",
  fontSize: "0.75rem",
  fontWeight: "400"
};
var l = {
  paddingTop: "1.875rem",
  paddingBottom: "{form.field.padding.y}"
};
var i = {
  root: o,
  input: l
};
export {
  i as default,
  l as input,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_iftalabel.js.map
