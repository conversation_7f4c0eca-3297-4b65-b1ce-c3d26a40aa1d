{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/listbox/index.ts"], "sourcesContent": ["import type { ListboxDesignTokens, ListboxTokenSections } from '@primeuix/themes/types/listbox';\n\nexport const root: ListboxTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    borderColor: '{form.field.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    shadow: '{form.field.shadow}',\n    borderRadius: '{form.field.border.radius}',\n    transitionDuration: '{form.field.transition.duration}'\n};\n\nexport const list: ListboxTokenSections.List = {\n    padding: '{list.padding}',\n    gap: '{list.gap}',\n    header: {\n        padding: '{list.header.padding}'\n    }\n};\n\nexport const option: ListboxTokenSections.Option = {\n    focusBackground: '{list.option.focus.background}',\n    selectedBackground: '{list.option.selected.background}',\n    selectedFocusBackground: '{list.option.selected.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    selectedColor: '{list.option.selected.color}',\n    selectedFocusColor: '{list.option.selected.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const optionGroup: ListboxTokenSections.OptionGroup = {\n    background: '{list.option.group.background}',\n    color: '{list.option.group.color}',\n    fontWeight: '{list.option.group.font.weight}',\n    padding: '{list.option.group.padding}'\n};\n\nexport const checkmark: ListboxTokenSections.Checkmark = {\n    color: '{list.option.color}',\n    gutterStart: '-0.5rem',\n    gutterEnd: '0.5rem'\n};\n\nexport const emptyMessage: ListboxTokenSections.EmptyMessage = {\n    padding: '{list.option.padding}'\n};\n\nexport const colorScheme: ListboxTokenSections.ColorScheme = {\n    light: {\n        option: {\n            stripedBackground: '{surface.50}'\n        }\n    },\n    dark: {\n        option: {\n            stripedBackground: '{surface.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    list,\n    option,\n    optionGroup,\n    checkmark,\n    emptyMessage,\n    colorScheme\n} satisfies ListboxDesignTokens;\n"], "mappings": ";;;;EAEa,YAAkC;EAC3C,oBAAY;EACZ,aAAA;EACA,oBAAa;EACb,OAAA;EACA,eAAO;EACP,QAAA;EACA,cAAQ;EACR,oBAAc;AACd;IACJ,IAAA;EAEa,SAAkC;EAC3C,KAAA;EACA,QAAK;IACL,SAAQ;EACJ;AACJ;IACJ,IAAA;EAEa,iBAAsC;EAC/C,oBAAiB;EACjB,yBAAoB;EACpB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,oBAAe;EACf,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,YAAA;EACT,OAAA;EACA,YAAO;EACP,SAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,aAAO;EACP,WAAa;AACb;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,mBAAQ;IACJ;EACJ;EACJ,MAAA;IACA,QAAM;MACF,mBAAQ;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,QAAA;EACA,aAAA;EACA,WAAA;EACA,cAAA;EACA,aAAA;AACA;", "names": []}