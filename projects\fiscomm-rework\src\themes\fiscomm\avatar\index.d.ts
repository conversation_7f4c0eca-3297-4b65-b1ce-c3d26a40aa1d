import type { AvatarTokenSections } from '@primeuix/themes/types/avatar';

export * from '@primeuix/themes/types/avatar';

declare const root: AvatarTokenSections.Root;
declare const icon: AvatarTokenSections.Icon;
declare const group: AvatarTokenSections.Group;
declare const lg: AvatarTokenSections.Lg;
declare const xl: AvatarTokenSections.Xl;
declare const _default: {
    root: AvatarTokenSections.Root;
    icon: AvatarTokenSections.Icon;
    group: AvatarTokenSections.Group;
    lg: AvatarTokenSections.Lg;
    xl: AvatarTokenSections.Xl;
};

export { _default as default, group, icon, lg, root, xl };
