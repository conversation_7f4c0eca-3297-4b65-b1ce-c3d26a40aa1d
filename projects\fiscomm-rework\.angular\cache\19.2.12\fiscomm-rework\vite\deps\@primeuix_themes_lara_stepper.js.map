{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/stepper/index.ts"], "sourcesContent": ["import type { StepperDesignTokens, StepperTokenSections } from '@primeuix/themes/types/stepper';\n\nexport const root: StepperTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const separator: StepperTokenSections.Separator = {\n    background: '{content.border.color}',\n    activeBackground: '{primary.color}',\n    margin: '0 0 0 1.625rem',\n    size: '2px'\n};\n\nexport const step: StepperTokenSections.Step = {\n    padding: '0.5rem',\n    gap: '1rem'\n};\n\nexport const stepHeader: StepperTokenSections.StepHeader = {\n    padding: '0',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    },\n    gap: '0.5rem'\n};\n\nexport const stepTitle: StepperTokenSections.StepTitle = {\n    color: '{text.muted.color}',\n    activeColor: '{primary.color}',\n    fontWeight: '500'\n};\n\nexport const stepNumber: StepperTokenSections.StepNumber = {\n    background: '{content.background}',\n    activeBackground: '{primary.color}',\n    borderColor: '{content.border.color}',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    activeColor: '{primary.contrast.color}',\n    size: '2.25rem',\n    fontSize: '1.125rem',\n    fontWeight: '500',\n    borderRadius: '50%',\n    shadow: 'none'\n};\n\nexport const steppanels: StepperTokenSections.Steppanels = {\n    padding: '0.875rem 0.5rem 1.125rem 0.5rem'\n};\n\nexport const steppanel: StepperTokenSections.Steppanel = {\n    background: '{content.background}',\n    color: '{content.color}',\n    padding: '0',\n    indent: '1rem'\n};\n\nexport default {\n    root,\n    separator,\n    step,\n    stepHeader,\n    stepTitle,\n    stepNumber,\n    steppanels,\n    steppanel\n} satisfies StepperDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAkC;AAC3C;IACJ,IAAA;EAEa,YAA4C;EACrD,kBAAY;EACZ,QAAA;EACA,MAAQ;AACR;IACJ,IAAA;EAEa,SAAkC;EAC3C,KAAA;AACA;IACJ,IAAA;EAEa,SAAA;EACT,cAAS;EACT,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,KAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,aAAO;EACP,YAAa;AACb;IACJ,IAAA;EAEa,YAAA;EACT,kBAAY;EACZ,aAAA;EACA,mBAAa;EACb,OAAA;EACA,aAAO;EACP,MAAA;EACA,UAAM;EACN,YAAU;EACV,cAAY;EACZ,QAAA;AACA;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,YAA4C;EACrD,OAAA;EACA,SAAO;EACP,QAAS;AACT;IACJ,IAAA;EAEO,MAAA;EACH,WAAA;EACA,MAAA;EACA,YAAA;EACA,WAAA;EACA,YAAA;EACA,YAAA;EACA,WAAA;AACA;", "names": []}