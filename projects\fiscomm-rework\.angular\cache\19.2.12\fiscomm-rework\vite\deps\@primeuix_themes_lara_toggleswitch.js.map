{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/toggleswitch/index.ts"], "sourcesContent": ["import type { ToggleSwitchDesignTokens, ToggleSwitchTokenSections } from '@primeuix/themes/types/toggleswitch';\n\nexport const root: ToggleSwitchTokenSections.Root = {\n    width: '3rem',\n    height: '1.75rem',\n    borderRadius: '30px',\n    gap: '0.25rem',\n    shadow: '{form.field.shadow}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    borderWidth: '1px',\n    borderColor: 'transparent',\n    hoverBorderColor: 'transparent',\n    checkedBorderColor: 'transparent',\n    checkedHoverBorderColor: 'transparent',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    transitionDuration: '{form.field.transition.duration}',\n    slideDuration: '0.2s'\n};\n\nexport const handle: ToggleSwitchTokenSections.Handle = {\n    borderRadius: '50%',\n    size: '1.25rem'\n};\n\nexport const colorScheme: ToggleSwitchTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.300}',\n            disabledBackground: '{form.field.disabled.background}',\n            hoverBackground: '{surface.400}',\n            checkedBackground: '{primary.color}',\n            checkedHoverBackground: '{primary.hover.color}'\n        },\n        handle: {\n            background: '{surface.0}',\n            disabledBackground: '{form.field.disabled.color}',\n            hoverBackground: '{surface.0}',\n            checkedBackground: '{surface.0}',\n            checkedHoverBackground: '{surface.0}',\n            color: '{text.muted.color}',\n            hoverColor: '{text.color}',\n            checkedColor: '{primary.color}',\n            checkedHoverColor: '{primary.hover.color}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.700}',\n            disabledBackground: '{surface.600}',\n            hoverBackground: '{surface.600}',\n            checkedBackground: '{primary.color}',\n            checkedHoverBackground: '{primary.hover.color}'\n        },\n        handle: {\n            background: '{surface.400}',\n            disabledBackground: '{surface.900}',\n            hoverBackground: '{surface.300}',\n            checkedBackground: '{surface.900}',\n            checkedHoverBackground: '{surface.900}',\n            color: '{surface.900}',\n            hoverColor: '{surface.800}',\n            checkedColor: '{primary.color}',\n            checkedHoverColor: '{primary.hover.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    handle,\n    colorScheme\n} satisfies ToggleSwitchDesignTokens;\n"], "mappings": ";;;;EAEa,OAAuC;EAChD,QAAO;EACP,cAAQ;EACR,KAAA;EACA,QAAK;EACL,WAAQ;IACR,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,aAAA;EACA,aAAa;EACb,kBAAa;EACb,oBAAkB;EAClB,yBAAoB;EACpB,oBAAA;EACA,oBAAoB;EACpB,eAAA;AACA;IACJ,IAAA;EAEa,cAA2C;EACpD,MAAA;AACA;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,oBAAY;MACZ,iBAAA;MACA,mBAAiB;MACjB,wBAAmB;IACnB;IACJ,QAAA;MACA,YAAQ;MACJ,oBAAY;MACZ,iBAAA;MACA,mBAAiB;MACjB,wBAAmB;MACnB,OAAA;MACA,YAAO;MACP,cAAY;MACZ,mBAAc;IACd;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,oBAAY;MACZ,iBAAA;MACA,mBAAiB;MACjB,wBAAmB;IACnB;IACJ,QAAA;MACA,YAAQ;MACJ,oBAAY;MACZ,iBAAA;MACA,mBAAiB;MACjB,wBAAmB;MACnB,OAAA;MACA,YAAO;MACP,cAAY;MACZ,mBAAc;IACd;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,aAAA;AACA;", "names": []}