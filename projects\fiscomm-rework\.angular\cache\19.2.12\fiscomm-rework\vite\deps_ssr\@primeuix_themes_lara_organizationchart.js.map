{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/organizationchart/index.ts"], "sourcesContent": ["import type { OrganizationChartDesignTokens, OrganizationChartTokenSections } from '@primeuix/themes/types/organizationchart';\n\nexport const root: OrganizationChartTokenSections.Root = {\n    gutter: '0.75rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const node: OrganizationChartTokenSections.Node = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    selectedColor: '{highlight.color}',\n    hoverColor: '{content.hover.color}',\n    padding: '1rem 1.25rem',\n    toggleablePadding: '1rem 1.25rem 1.5rem 1.25rem',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const nodeToggleButton: OrganizationChartTokenSections.NodeToggleButton = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    borderColor: '{content.border.color}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    size: '1.75rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const connector: OrganizationChartTokenSections.Connector = {\n    color: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    height: '24px'\n};\n\nexport default {\n    root,\n    node,\n    nodeToggleButton,\n    connector\n} satisfies OrganizationChartDesignTokens;\n"], "mappings": ";;;;;EAEa,QAA4C;EACrD,oBAAQ;AACR;IACJ,IAAA;EAEa,YAA4C;EACrD,iBAAY;EACZ,oBAAiB;EACjB,aAAA;EACA,OAAA;EACA,eAAO;EACP,YAAA;EACA,SAAA;EACA,mBAAS;EACT,cAAA;AACA;IACJ,IAAA;EAEa,YAAA;EACT,iBAAY;EACZ,aAAA;EACA,OAAA;EACA,YAAO;EACP,MAAA;EACA,cAAM;EACN,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;EACT,cAAO;EACP,QAAA;AACA;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,kBAAA;EACA,WAAA;AACA;", "names": []}