{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tabview/index.ts"], "sourcesContent": ["import type { TabViewDesignTokens, TabViewTokenSections } from '@primeuix/themes/types/tabview';\n\nexport const root: TabViewTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const tabList: TabViewTokenSections.TabList = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}'\n};\n\nexport const tab: TabViewTokenSections.Tab = {\n    borderColor: '{content.border.color}',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}'\n};\n\nexport const tabPanel: TabViewTokenSections.TabPanel = {\n    background: '{content.background}',\n    color: '{content.color}'\n};\n\nexport const navButton: TabViewTokenSections.NavButton = {\n    background: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}'\n};\n\nexport const colorScheme: TabViewTokenSections.ColorScheme = {\n    light: {\n        navButton: {\n            shadow: '0px 0px 10px 50px rgba(255, 255, 255, 0.6)'\n        }\n    },\n    dark: {\n        navButton: {\n            shadow: '0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)'\n        }\n    }\n};\n\nexport default {\n    root,\n    tabList,\n    tab,\n    tabPanel,\n    navButton,\n    colorScheme\n} satisfies TabViewDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAkC;AAC3C;IACJ,IAAA;EAEa,YAAwC;EACjD,aAAY;AACZ;IACJ,IAAA;EAEa,aAAgC;EACzC,mBAAa;EACb,OAAA;EACA,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,YAA0C;EACnD,OAAA;AACA;IACJ,IAAA;EAEa,YAA4C;EACrD,OAAA;EACA,YAAO;AACP;IACJ,IAAA;EAEa,OAAA;IACT,WAAO;MACH,QAAW;IACP;EACJ;EACJ,MAAA;IACA,WAAM;MACF,QAAW;IACP;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,KAAA;EACA,UAAA;EACA,WAAA;EACA,aAAA;AACA;", "names": []}