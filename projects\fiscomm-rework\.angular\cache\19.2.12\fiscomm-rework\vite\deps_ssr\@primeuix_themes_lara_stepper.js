import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/stepper/index.mjs
var o = {
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "{content.border.color}",
  activeBackground: "{primary.color}",
  margin: "0 0 0 1.625rem",
  size: "2px"
};
var e = {
  padding: "0.5rem",
  gap: "1rem"
};
var t = {
  padding: "0",
  borderRadius: "{content.border.radius}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  },
  gap: "0.5rem"
};
var n = {
  color: "{text.muted.color}",
  activeColor: "{primary.color}",
  fontWeight: "500"
};
var a = {
  background: "{content.background}",
  activeBackground: "{primary.color}",
  borderColor: "{content.border.color}",
  activeBorderColor: "{primary.color}",
  color: "{text.muted.color}",
  activeColor: "{primary.contrast.color}",
  size: "2.25rem",
  fontSize: "1.125rem",
  fontWeight: "500",
  borderRadius: "50%",
  shadow: "none"
};
var c = {
  padding: "0.875rem 0.5rem 1.125rem 0.5rem"
};
var i = {
  background: "{content.background}",
  color: "{content.color}",
  padding: "0",
  indent: "1rem"
};
var d = {
  root: o,
  separator: r,
  step: e,
  stepHeader: t,
  stepTitle: n,
  stepNumber: a,
  steppanels: c,
  steppanel: i
};
export {
  d as default,
  o as root,
  r as separator,
  e as step,
  t as stepHeader,
  a as stepNumber,
  n as stepTitle,
  i as steppanel,
  c as steppanels
};
//# sourceMappingURL=@primeuix_themes_lara_stepper.js.map
