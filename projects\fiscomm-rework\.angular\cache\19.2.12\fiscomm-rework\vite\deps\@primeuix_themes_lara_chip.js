import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/chip/index.mjs
var o = {
  borderRadius: "16px",
  paddingX: "0.875rem",
  paddingY: "0.625rem",
  gap: "0.5rem",
  transitionDuration: "{transition.duration}"
};
var r = {
  width: "2rem",
  height: "2rem"
};
var e = {
  size: "1rem"
};
var c = {
  size: "1rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var i = {
  light: {
    root: {
      background: "{surface.100}",
      color: "{surface.800}"
    },
    icon: {
      color: "{surface.800}"
    },
    removeIcon: {
      color: "{surface.800}"
    }
  },
  dark: {
    root: {
      background: "{surface.800}",
      color: "{surface.0}"
    },
    icon: {
      color: "{surface.0}"
    },
    removeIcon: {
      color: "{surface.0}"
    }
  }
};
var s = {
  root: o,
  image: r,
  icon: e,
  removeIcon: c,
  colorScheme: i
};
export {
  i as colorScheme,
  s as default,
  e as icon,
  r as image,
  c as removeIcon,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_chip.js.map
