import type { GalleriaTokenSections } from '@primeuix/themes/types/galleria';

export * from '@primeuix/themes/types/galleria';

declare const root: GalleriaTokenSections.Root;
declare const navButton: GalleriaTokenSections.NavButton;
declare const navIcon: GalleriaTokenSections.NavIcon;
declare const thumbnailsContent: GalleriaTokenSections.ThumbnailsContent;
declare const thumbnailNavButton: GalleriaTokenSections.ThumbnailNavButton;
declare const thumbnailNavButtonIcon: GalleriaTokenSections.ThumbnailNavButtonIcon;
declare const caption: GalleriaTokenSections.Caption;
declare const indicatorList: GalleriaTokenSections.IndicatorList;
declare const indicatorButton: GalleriaTokenSections.IndicatorButton;
declare const insetIndicatorList: GalleriaTokenSections.InsetIndicatorList;
declare const insetIndicatorButton: GalleriaTokenSections.InsetIndicatorButton;
declare const closeButton: GalleriaTokenSections.CloseButton;
declare const closeButtonIcon: GalleriaTokenSections.CloseButtonIcon;
declare const colorScheme: GalleriaTokenSections.ColorScheme;
declare const _default: {
    root: GalleriaTokenSections.Root;
    navButton: GalleriaTokenSections.NavButton;
    navIcon: GalleriaTokenSections.NavIcon;
    thumbnailsContent: GalleriaTokenSections.ThumbnailsContent;
    thumbnailNavButton: GalleriaTokenSections.ThumbnailNavButton;
    thumbnailNavButtonIcon: GalleriaTokenSections.ThumbnailNavButtonIcon;
    caption: GalleriaTokenSections.Caption;
    indicatorList: GalleriaTokenSections.IndicatorList;
    indicatorButton: GalleriaTokenSections.IndicatorButton;
    insetIndicatorList: GalleriaTokenSections.InsetIndicatorList;
    insetIndicatorButton: GalleriaTokenSections.InsetIndicatorButton;
    closeButton: GalleriaTokenSections.CloseButton;
    closeButtonIcon: GalleriaTokenSections.CloseButtonIcon;
    colorScheme: GalleriaTokenSections.ColorScheme;
};

export { caption, closeButton, closeButtonIcon, colorScheme, _default as default, indicatorButton, indicatorList, insetIndicatorButton, insetIndicatorList, navButton, navIcon, root, thumbnailNavButton, thumbnailNavButtonIcon, thumbnailsContent };
