{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/ripple/index.ts"], "sourcesContent": ["import type { RippleDesignTokens, RippleTokenSections } from '@primeuix/themes/types/ripple';\n\nexport const colorScheme: RippleTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: 'rgba(0,0,0,0.1)'\n        }\n    },\n    dark: {\n        root: {\n            background: 'rgba(255,255,255,0.3)'\n        }\n    }\n};\n\nexport default {\n    colorScheme\n} satisfies RippleDesignTokens;\n"], "mappings": ";;;;;EAEa,OAAA;IACT,MAAO;MACH,YAAM;IACF;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;IACF;EACJ;AACJ;IACJ,IAAA;EAEO,aAAA;AACH;", "names": []}