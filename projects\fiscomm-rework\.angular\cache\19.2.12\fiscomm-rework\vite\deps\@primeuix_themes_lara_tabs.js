import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/tabs/index.mjs
var o = {
  transitionDuration: "{transition.duration}"
};
var r = {
  borderWidth: "0",
  background: "{content.background}",
  borderColor: "{content.border.color}"
};
var t = {
  borderWidth: "2px 0 0 0",
  borderColor: "transparent",
  hoverBorderColor: "transparent",
  activeBorderColor: "{primary.color}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  activeColor: "{primary.color}",
  padding: "1rem 1.25rem",
  fontWeight: "700",
  margin: "0",
  gap: "0.5rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  }
};
var n = {
  background: "{content.background}",
  color: "{content.color}",
  padding: "0.875rem 1.125rem 1.125rem 1.125rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  }
};
var c = {
  background: "{content.background}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  width: "2.5rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  }
};
var e = {
  height: "0",
  bottom: "0",
  background: "transparent"
};
var a = {
  light: {
    navButton: {
      shadow: "0px 0px 10px 50px rgba(255, 255, 255, 0.6)"
    },
    tab: {
      background: "{surface.50}",
      hoverBackground: "{surface.100}",
      activeBackground: "{surface.0}"
    }
  },
  dark: {
    navButton: {
      shadow: "0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"
    },
    tab: {
      background: "{surface.800}",
      hoverBackground: "{surface.700}",
      activeBackground: "{surface.900}"
    }
  }
};
var s = {
  root: o,
  tablist: r,
  tab: t,
  tabpanel: n,
  navButton: c,
  activeBar: e,
  colorScheme: a
};
export {
  e as activeBar,
  a as colorScheme,
  s as default,
  c as navButton,
  o as root,
  t as tab,
  r as tablist,
  n as tabpanel
};
//# sourceMappingURL=@primeuix_themes_lara_tabs.js.map
