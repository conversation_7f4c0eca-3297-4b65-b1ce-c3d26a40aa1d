{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tooltip/index.ts"], "sourcesContent": ["import type { TooltipDesignTokens, TooltipTokenSections } from '@primeuix/themes/types/tooltip';\n\nexport const root: TooltipTokenSections.Root = {\n    maxWidth: '12.5rem',\n    gutter: '0.25rem',\n    shadow: '{overlay.popover.shadow}',\n    padding: '0.625rem 0.75rem',\n    borderRadius: '{overlay.popover.border.radius}'\n};\n\nexport const colorScheme: TooltipTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.700}',\n            color: '{surface.0}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.700}',\n            color: '{surface.0}'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies TooltipDesignTokens;\n"], "mappings": ";;;;;EAEa,UAAkC;EAC3C,QAAU;EACV,QAAQ;EACR,SAAQ;EACR,cAAS;AACT;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,OAAA;IACA;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,aAAA;AACA;", "names": []}