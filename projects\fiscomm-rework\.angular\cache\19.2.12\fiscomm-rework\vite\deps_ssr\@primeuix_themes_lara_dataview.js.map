{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/dataview/index.ts"], "sourcesContent": ["import type { DataViewDesignTokens, DataViewTokenSections } from '@primeuix/themes/types/dataview';\n\nexport const root: DataViewTokenSections.Root = {\n    borderColor: '{content.border.color}',\n    borderWidth: '1px',\n    borderRadius: '4px',\n    padding: '0'\n};\n\nexport const header: DataViewTokenSections.Header = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0',\n    padding: '0.875rem 1.125rem',\n    borderRadius: '5px 5px 0 0'\n};\n\nexport const content: DataViewTokenSections.Content = {\n    background: '{content.background}',\n    color: '{content.color}',\n    borderColor: 'transparent',\n    borderWidth: '0',\n    padding: '0',\n    borderRadius: '5px'\n};\n\nexport const footer: DataViewTokenSections.Footer = {\n    background: '{content.background}',\n    color: '{content.color}',\n    borderColor: '{content.border.color}',\n    borderWidth: '1px 0 0 0',\n    padding: '0.875rem 1.125rem',\n    borderRadius: '0 0 5px 5px'\n};\n\nexport const paginatorTop: DataViewTokenSections.PaginatorTop = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const paginatorBottom: DataViewTokenSections.PaginatorBottom = {\n    borderColor: '{content.border.color}',\n    borderWidth: '1px 0 0 0'\n};\n\nexport const colorScheme: DataViewTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    content,\n    footer,\n    paginatorTop,\n    paginatorBottom,\n    colorScheme\n} satisfies DataViewDesignTokens;\n"], "mappings": ";;;;;EAEa,aAAmC;EAC5C,aAAa;EACb,cAAa;EACb,SAAA;AACA;IACJ,IAAA;EAEa,aAAuC;EAChD,aAAa;EACb,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,YAAyC;EAClD,OAAA;EACA,aAAO;EACP,aAAa;EACb,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,YAAuC;EAChD,OAAA;EACA,aAAO;EACP,aAAa;EACb,SAAA;EACA,cAAS;AACT;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,OAAA;IACT,QAAO;MACH,YAAQ;MACJ,OAAA;IACA;EACJ;EACJ,MAAA;IACA,QAAM;MACF,YAAQ;MACJ,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,SAAA;EACA,QAAA;EACA,cAAA;EACA,iBAAA;EACA,aAAA;AACA;", "names": []}