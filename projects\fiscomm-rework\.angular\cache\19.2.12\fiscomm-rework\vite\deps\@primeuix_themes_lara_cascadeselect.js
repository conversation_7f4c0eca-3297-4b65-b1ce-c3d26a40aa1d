import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/cascadeselect/index.mjs
var o = {
  background: "{form.field.background}",
  disabledBackground: "{form.field.disabled.background}",
  filledBackground: "{form.field.filled.background}",
  filledHoverBackground: "{form.field.filled.hover.background}",
  filledFocusBackground: "{form.field.filled.focus.background}",
  borderColor: "{form.field.border.color}",
  hoverBorderColor: "{form.field.hover.border.color}",
  focusBorderColor: "{form.field.focus.border.color}",
  invalidBorderColor: "{form.field.invalid.border.color}",
  color: "{form.field.color}",
  disabledColor: "{form.field.disabled.color}",
  placeholderColor: "{form.field.placeholder.color}",
  invalidPlaceholderColor: "{form.field.invalid.placeholder.color}",
  shadow: "{form.field.shadow}",
  paddingX: "{form.field.padding.x}",
  paddingY: "{form.field.padding.y}",
  borderRadius: "{form.field.border.radius}",
  focusRing: {
    width: "{form.field.focus.ring.width}",
    style: "{form.field.focus.ring.style}",
    color: "{form.field.focus.ring.color}",
    offset: "{form.field.focus.ring.offset}",
    shadow: "{form.field.focus.ring.shadow}"
  },
  transitionDuration: "{form.field.transition.duration}",
  sm: {
    fontSize: "{form.field.sm.font.size}",
    paddingX: "{form.field.sm.padding.x}",
    paddingY: "{form.field.sm.padding.y}"
  },
  lg: {
    fontSize: "{form.field.lg.font.size}",
    paddingX: "{form.field.lg.padding.x}",
    paddingY: "{form.field.lg.padding.y}"
  }
};
var r = {
  width: "2.5rem",
  color: "{form.field.icon.color}"
};
var d = {
  background: "{overlay.select.background}",
  borderColor: "{overlay.select.border.color}",
  borderRadius: "{overlay.select.border.radius}",
  color: "{overlay.select.color}",
  shadow: "{overlay.select.shadow}"
};
var l = {
  padding: "{list.padding}",
  gap: "{list.gap}",
  mobileIndent: "1.25rem"
};
var e = {
  focusBackground: "{list.option.focus.background}",
  selectedBackground: "{list.option.selected.background}",
  selectedFocusBackground: "{list.option.selected.focus.background}",
  color: "{list.option.color}",
  focusColor: "{list.option.focus.color}",
  selectedColor: "{list.option.selected.color}",
  selectedFocusColor: "{list.option.selected.focus.color}",
  padding: "{list.option.padding}",
  borderRadius: "{list.option.border.radius}",
  icon: {
    color: "{list.option.icon.color}",
    focusColor: "{list.option.icon.focus.color}",
    size: "0.875rem"
  }
};
var i = {
  color: "{form.field.icon.color}"
};
var f = {
  root: o,
  dropdown: r,
  overlay: d,
  list: l,
  option: e,
  clearIcon: i
};
export {
  i as clearIcon,
  f as default,
  r as dropdown,
  l as list,
  e as option,
  d as overlay,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_cascadeselect.js.map
