{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/base/index.ts"], "sourcesContent": ["import type { LaraBaseDesignTokens, LaraBaseTokenSections } from './index.d';\n\nexport const primitive: LaraBaseTokenSections.Primitive = {\n    borderRadius: {\n        none: '0',\n        xs: '2px',\n        sm: '4px',\n        md: '6px',\n        lg: '8px',\n        xl: '12px'\n    },\n    emerald: { 50: '#ecfdf5', 100: '#d1fae5', 200: '#a7f3d0', 300: '#6ee7b7', 400: '#34d399', 500: '#10b981', 600: '#059669', 700: '#047857', 800: '#065f46', 900: '#064e3b', 950: '#022c22' },\n    green: { 50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80', 500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d', 950: '#052e16' },\n    lime: { 50: '#f7fee7', 100: '#ecfccb', 200: '#d9f99d', 300: '#bef264', 400: '#a3e635', 500: '#84cc16', 600: '#65a30d', 700: '#4d7c0f', 800: '#3f6212', 900: '#365314', 950: '#1a2e05' },\n    red: { 50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171', 500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d', 950: '#450a0a' },\n    orange: { 50: '#fff7ed', 100: '#ffedd5', 200: '#fed7aa', 300: '#fdba74', 400: '#fb923c', 500: '#f97316', 600: '#ea580c', 700: '#c2410c', 800: '#9a3412', 900: '#7c2d12', 950: '#431407' },\n    amber: { 50: '#fffbeb', 100: '#fef3c7', 200: '#fde68a', 300: '#fcd34d', 400: '#fbbf24', 500: '#f59e0b', 600: '#d97706', 700: '#b45309', 800: '#92400e', 900: '#78350f', 950: '#451a03' },\n    yellow: { 50: '#fefce8', 100: '#fef9c3', 200: '#fef08a', 300: '#fde047', 400: '#facc15', 500: '#eab308', 600: '#ca8a04', 700: '#a16207', 800: '#854d0e', 900: '#713f12', 950: '#422006' },\n    teal: { 50: '#f0fdfa', 100: '#ccfbf1', 200: '#99f6e4', 300: '#5eead4', 400: '#2dd4bf', 500: '#14b8a6', 600: '#0d9488', 700: '#0f766e', 800: '#115e59', 900: '#134e4a', 950: '#042f2e' },\n    cyan: { 50: '#ecfeff', 100: '#cffafe', 200: '#a5f3fc', 300: '#67e8f9', 400: '#22d3ee', 500: '#06b6d4', 600: '#0891b2', 700: '#0e7490', 800: '#155e75', 900: '#164e63', 950: '#083344' },\n    sky: { 50: '#f0f9ff', 100: '#e0f2fe', 200: '#bae6fd', 300: '#7dd3fc', 400: '#38bdf8', 500: '#0ea5e9', 600: '#0284c7', 700: '#0369a1', 800: '#075985', 900: '#0c4a6e', 950: '#082f49' },\n    blue: { 50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a', 950: '#172554' },\n    indigo: { 50: '#eef2ff', 100: '#e0e7ff', 200: '#c7d2fe', 300: '#a5b4fc', 400: '#818cf8', 500: '#6366f1', 600: '#4f46e5', 700: '#4338ca', 800: '#3730a3', 900: '#312e81', 950: '#1e1b4b' },\n    violet: { 50: '#f5f3ff', 100: '#ede9fe', 200: '#ddd6fe', 300: '#c4b5fd', 400: '#a78bfa', 500: '#8b5cf6', 600: '#7c3aed', 700: '#6d28d9', 800: '#5b21b6', 900: '#4c1d95', 950: '#2e1065' },\n    purple: { 50: '#faf5ff', 100: '#f3e8ff', 200: '#e9d5ff', 300: '#d8b4fe', 400: '#c084fc', 500: '#a855f7', 600: '#9333ea', 700: '#7e22ce', 800: '#6b21a8', 900: '#581c87', 950: '#3b0764' },\n    fuchsia: { 50: '#fdf4ff', 100: '#fae8ff', 200: '#f5d0fe', 300: '#f0abfc', 400: '#e879f9', 500: '#d946ef', 600: '#c026d3', 700: '#a21caf', 800: '#86198f', 900: '#701a75', 950: '#4a044e' },\n    pink: { 50: '#fdf2f8', 100: '#fce7f3', 200: '#fbcfe8', 300: '#f9a8d4', 400: '#f472b6', 500: '#ec4899', 600: '#db2777', 700: '#be185d', 800: '#9d174d', 900: '#831843', 950: '#500724' },\n    rose: { 50: '#fff1f2', 100: '#ffe4e6', 200: '#fecdd3', 300: '#fda4af', 400: '#fb7185', 500: '#f43f5e', 600: '#e11d48', 700: '#be123c', 800: '#9f1239', 900: '#881337', 950: '#4c0519' },\n    slate: { 50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8', 500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a', 950: '#020617' },\n    gray: { 50: '#f9fafb', 100: '#f3f4f6', 200: '#e5e7eb', 300: '#d1d5db', 400: '#9ca3af', 500: '#6b7280', 600: '#4b5563', 700: '#374151', 800: '#1f2937', 900: '#111827', 950: '#030712' },\n    zinc: { 50: '#fafafa', 100: '#f4f4f5', 200: '#e4e4e7', 300: '#d4d4d8', 400: '#a1a1aa', 500: '#71717a', 600: '#52525b', 700: '#3f3f46', 800: '#27272a', 900: '#18181b', 950: '#09090b' },\n    neutral: { 50: '#fafafa', 100: '#f5f5f5', 200: '#e5e5e5', 300: '#d4d4d4', 400: '#a3a3a3', 500: '#737373', 600: '#525252', 700: '#404040', 800: '#262626', 900: '#171717', 950: '#0a0a0a' },\n    stone: { 50: '#fafaf9', 100: '#f5f5f4', 200: '#e7e5e4', 300: '#d6d3d1', 400: '#a8a29e', 500: '#78716c', 600: '#57534e', 700: '#44403c', 800: '#292524', 900: '#1c1917', 950: '#0c0a09' }\n};\n\nexport const semantic: LaraBaseTokenSections.Semantic = {\n    transitionDuration: '0.2s',\n    focusRing: {\n        width: '0',\n        style: 'none',\n        color: 'transparent',\n        offset: '0'\n    },\n    disabledOpacity: '0.6',\n    iconSize: '1rem',\n    anchorGutter: '2px',\n    primary: {\n        50: '{emerald.50}',\n        100: '{emerald.100}',\n        200: '{emerald.200}',\n        300: '{emerald.300}',\n        400: '{emerald.400}',\n        500: '{emerald.500}',\n        600: '{emerald.600}',\n        700: '{emerald.700}',\n        800: '{emerald.800}',\n        900: '{emerald.900}',\n        950: '{emerald.950}'\n    },\n    formField: {\n        paddingX: '0.75rem',\n        paddingY: '0.625rem',\n        sm: {\n            fontSize: '0.875rem',\n            paddingX: '0.625rem',\n            paddingY: '0.5rem'\n        },\n        lg: {\n            fontSize: '1.125rem',\n            paddingX: '0.875rem',\n            paddingY: '0.75rem'\n        },\n        borderRadius: '{border.radius.md}',\n        focusRing: {\n            width: '{focus.ring.width}',\n            style: '{focus.ring.style}',\n            color: '{focus.ring.color}',\n            offset: '{focus.ring.offset}',\n            shadow: '{focus.ring.shadow}'\n        },\n        transitionDuration: '{transition.duration}'\n    },\n    list: {\n        padding: '0.5rem 0',\n        gap: '0',\n        header: {\n            padding: '0.625rem 1rem 0 1rem'\n        },\n        option: {\n            padding: '0.625rem 1rem',\n            borderRadius: '0'\n        },\n        optionGroup: {\n            padding: '0.625rem 1rem',\n            fontWeight: '600'\n        }\n    },\n    content: {\n        borderRadius: '{border.radius.md}'\n    },\n    mask: {\n        transitionDuration: '0.15s'\n    },\n    navigation: {\n        list: {\n            padding: '0.5rem 0',\n            gap: '0'\n        },\n        item: {\n            padding: '0.625rem 1rem',\n            borderRadius: '0',\n            gap: '0.5rem'\n        },\n        submenuLabel: {\n            padding: '0.625rem 1rem',\n            fontWeight: '600'\n        },\n        submenuIcon: {\n            size: '0.875rem'\n        }\n    },\n    overlay: {\n        select: {\n            borderRadius: '{border.radius.md}',\n            shadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'\n        },\n        popover: {\n            borderRadius: '{border.radius.md}',\n            padding: '1rem',\n            shadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        modal: {\n            borderRadius: '{border.radius.xl}',\n            padding: '1.5rem',\n            shadow: '0 1px 3px rgba(0, 0, 0, 0.3)'\n        },\n        navigation: {\n            shadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'\n        }\n    },\n    colorScheme: {\n        light: {\n            surface: {\n                0: '#ffffff',\n                50: '{slate.50}',\n                100: '{slate.100}',\n                200: '{slate.200}',\n                300: '{slate.300}',\n                400: '{slate.400}',\n                500: '{slate.500}',\n                600: '{slate.600}',\n                700: '{slate.700}',\n                800: '{slate.800}',\n                900: '{slate.900}',\n                950: '{slate.950}'\n            },\n            primary: {\n                color: '{primary.500}',\n                contrastColor: '#ffffff',\n                hoverColor: '{primary.600}',\n                activeColor: '{primary.700}'\n            },\n            highlight: {\n                background: '{primary.50}',\n                focusBackground: '{primary.100}',\n                color: '{primary.700}',\n                focusColor: '{primary.800}'\n            },\n            focusRing: {\n                shadow: '0 0 0 0.2rem {primary.200}'\n            },\n            mask: {\n                background: 'rgba(0,0,0,0.4)',\n                color: '{surface.200}'\n            },\n            formField: {\n                background: '{surface.0}',\n                disabledBackground: '{surface.200}',\n                filledBackground: '{surface.50}',\n                filledHoverBackground: '{surface.50}',\n                filledFocusBackground: '{surface.0}',\n                borderColor: '{surface.300}',\n                hoverBorderColor: '{primary.color}',\n                focusBorderColor: '{primary.color}',\n                invalidBorderColor: '{red.400}',\n                color: '{surface.700}',\n                disabledColor: '{surface.500}',\n                placeholderColor: '{surface.500}',\n                invalidPlaceholderColor: '{red.600}',\n                floatLabelColor: '{surface.500}',\n                floatLabelFocusColor: '{primary.600}',\n                floatLabelActiveColor: '{surface.500}',\n                floatLabelInvalidColor: '{form.field.invalid.placeholder.color}',\n                iconColor: '{surface.500}',\n                shadow: 'none'\n            },\n            text: {\n                color: '{surface.700}',\n                hoverColor: '{surface.800}',\n                mutedColor: '{surface.500}',\n                hoverMutedColor: '{surface.600}'\n            },\n            content: {\n                background: '{surface.0}',\n                hoverBackground: '{surface.100}',\n                borderColor: '{surface.200}',\n                color: '{text.color}',\n                hoverColor: '{text.hover.color}'\n            },\n            overlay: {\n                select: {\n                    background: '{surface.0}',\n                    borderColor: '{surface.200}',\n                    color: '{text.color}'\n                },\n                popover: {\n                    background: '{surface.0}',\n                    borderColor: '{surface.200}',\n                    color: '{text.color}'\n                },\n                modal: {\n                    background: '{surface.0}',\n                    borderColor: '{surface.200}',\n                    color: '{text.color}'\n                }\n            },\n            list: {\n                option: {\n                    focusBackground: '{surface.100}',\n                    selectedBackground: '{highlight.background}',\n                    selectedFocusBackground: '{highlight.focus.background}',\n                    color: '{text.color}',\n                    focusColor: '{text.hover.color}',\n                    selectedColor: '{highlight.color}',\n                    selectedFocusColor: '{highlight.focus.color}',\n                    icon: {\n                        color: '{surface.400}',\n                        focusColor: '{surface.500}'\n                    }\n                },\n                optionGroup: {\n                    background: 'transparent',\n                    color: '{text.color}'\n                }\n            },\n            navigation: {\n                item: {\n                    focusBackground: '{surface.100}',\n                    activeBackground: '{surface.100}',\n                    color: '{text.color}',\n                    focusColor: '{text.hover.color}',\n                    activeColor: '{text.hover.color}',\n                    icon: {\n                        color: '{surface.400}',\n                        focusColor: '{surface.500}',\n                        activeColor: '{surface.500}'\n                    }\n                },\n                submenuLabel: {\n                    background: 'transparent',\n                    color: '{text.color}'\n                },\n                submenuIcon: {\n                    color: '{surface.400}',\n                    focusColor: '{surface.500}',\n                    activeColor: '{surface.500}'\n                }\n            }\n        },\n        dark: {\n            surface: {\n                0: '#ffffff',\n                50: '{zinc.50}',\n                100: '{zinc.100}',\n                200: '{zinc.200}',\n                300: '{zinc.300}',\n                400: '{zinc.400}',\n                500: '{zinc.500}',\n                600: '{zinc.600}',\n                700: '{zinc.700}',\n                800: '{zinc.800}',\n                900: '{zinc.900}',\n                950: '{zinc.950}'\n            },\n            primary: {\n                color: '{primary.400}',\n                contrastColor: '{surface.900}',\n                hoverColor: '{primary.300}',\n                activeColor: '{primary.200}'\n            },\n            highlight: {\n                background: 'color-mix(in srgb, {primary.400}, transparent 84%)',\n                focusBackground: 'color-mix(in srgb, {primary.400}, transparent 76%)',\n                color: 'rgba(255,255,255,.87)',\n                focusColor: 'rgba(255,255,255,.87)'\n            },\n            focusRing: {\n                shadow: '0 0 0 0.2rem color-mix(in srgb, {primary.color}, transparent 80%)'\n            },\n            mask: {\n                background: 'rgba(0,0,0,0.6)',\n                color: '{surface.200}'\n            },\n            formField: {\n                background: '{surface.950}',\n                disabledBackground: '{surface.700}',\n                filledBackground: '{surface.800}',\n                filledHoverBackground: '{surface.800}',\n                filledFocusBackground: '{surface.950}',\n                borderColor: '{surface.600}',\n                hoverBorderColor: '{primary.color}',\n                focusBorderColor: '{primary.color}',\n                invalidBorderColor: '{red.300}',\n                color: '{surface.0}',\n                disabledColor: '{surface.400}',\n                placeholderColor: '{surface.400}',\n                invalidPlaceholderColor: '{red.400}',\n                floatLabelColor: '{surface.400}',\n                floatLabelFocusColor: '{primary.color}',\n                floatLabelActiveColor: '{surface.400}',\n                floatLabelInvalidColor: '{form.field.invalid.placeholder.color}',\n                iconColor: '{surface.400}',\n                shadow: 'none'\n            },\n            text: {\n                color: '{surface.0}',\n                hoverColor: '{surface.0}',\n                mutedColor: '{surface.400}',\n                hoverMutedColor: '{surface.300}'\n            },\n            content: {\n                background: '{surface.900}',\n                hoverBackground: '{surface.800}',\n                borderColor: '{surface.700}',\n                color: '{text.color}',\n                hoverColor: '{text.hover.color}'\n            },\n            overlay: {\n                select: {\n                    background: '{surface.900}',\n                    borderColor: '{surface.700}',\n                    color: '{text.color}'\n                },\n                popover: {\n                    background: '{surface.900}',\n                    borderColor: '{surface.700}',\n                    color: '{text.color}'\n                },\n                modal: {\n                    background: '{surface.900}',\n                    borderColor: '{surface.700}',\n                    color: '{text.color}'\n                }\n            },\n            list: {\n                option: {\n                    focusBackground: '{surface.800}',\n                    selectedBackground: '{highlight.background}',\n                    selectedFocusBackground: '{highlight.focus.background}',\n                    color: '{text.color}',\n                    focusColor: '{text.hover.color}',\n                    selectedColor: '{highlight.color}',\n                    selectedFocusColor: '{highlight.focus.color}',\n                    icon: {\n                        color: '{surface.500}',\n                        focusColor: '{surface.400}'\n                    }\n                },\n                optionGroup: {\n                    background: 'transparent',\n                    color: '{text.color}'\n                }\n            },\n            navigation: {\n                item: {\n                    focusBackground: '{surface.800}',\n                    activeBackground: '{surface.800}',\n                    color: '{text.color}',\n                    focusColor: '{text.hover.color}',\n                    activeColor: '{text.hover.color}',\n                    icon: {\n                        color: '{surface.500}',\n                        focusColor: '{surface.400}',\n                        activeColor: '{surface.400}'\n                    }\n                },\n                submenuLabel: {\n                    background: 'transparent',\n                    color: '{text.color}'\n                },\n                submenuIcon: {\n                    color: '{surface.500}',\n                    focusColor: '{surface.400}',\n                    activeColor: '{surface.400}'\n                }\n            }\n        }\n    }\n};\n\nexport default {\n    primitive,\n    semantic\n} satisfies LaraBaseDesignTokens;\n"], "mappings": ";;;;EAEa,cAA6C;IACtD,MAAA;IACI,IAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;EACJ;EACJ,SAAA;IACA,IAAA;IACA,KAAS;IACT,KAAQ;IACR,KAAO;IACP,KAAQ;IACR,KAAS;IACT,KAAQ;IACR,KAAQ;IACR,KAAQ;IACR,KAAO;IACP,KAAQ;EACR;EACA,OAAQ;IACR,IAAQ;IACR,KAAS;IACT,KAAQ;IACR,KAAQ;IACR,KAAS;IACT,KAAQ;IACR,KAAQ;IACR,KAAS;IACT,KAAS;IACb,KAAA;IAEa,KAAA;EACT;EACA,MAAA;IACI,IAAA;IACA,KAAO;IACP,KAAO;IACP,KAAA;IACJ,KAAA;IACA,KAAA;IACA,KAAA;IACA,KAAA;IACA,KAAS;IACL,KAAI;IACJ,KAAK;EACL;EACA,KAAK;IACL,IAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACT,KAAA;IACA,KAAA;IACI,KAAA;IACA,KAAA;EACA;EAAI,QACA;IACA,IAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAI;IACA,KAAA;IACA,KAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAA;IACA,KAAA;IACI,KAAA;EAAO;EACA,OACP;IACA,IAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAM;IACF,KAAA;IACA,KAAK;IACL,KAAA;IACI,KAAA;IACJ,KAAA;EACA;EAAQ,QACJ;IACA,IAAA;IACJ,KAAA;IACA,KAAA;IACI,KAAA;IACA,KAAA;IACJ,KAAA;IACJ,KAAA;IACA,KAAS;IACL,KAAA;IACJ,KAAA;IACA,KAAM;EACF;EACJ,MAAA;IACA,IAAA;IACI,KAAM;IACF,KAAA;IACA,KAAK;IACT,KAAA;IACA,KAAM;IACF,KAAA;IACA,KAAA;IACA,KAAK;IACT,KAAA;IACA,KAAA;EAAc;EACD,MACT;IACJ,IAAA;IACA,KAAA;IACI,KAAA;IACJ,KAAA;IACJ,KAAA;IACA,KAAS;IACL,KAAA;IACI,KAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAA;EAAS;EACS,KACd;IACA,IAAA;IACJ,KAAA;IACA,KAAO;IACH,KAAA;IACA,KAAA;IACA,KAAA;IACJ,KAAA;IACA,KAAA;IACI,KAAA;IACJ,KAAA;IACJ,KAAA;EACA;EACI,MAAA;IACI,IAAA;IAAS,KACF;IAAA,KACH;IAAI,KACJ;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;EAAK;EACA,QACL;IACJ,IAAA;IACA,KAAA;IAAS,KACL;IAAO,KACP;IAAe,KACf;IAAY,KACZ;IACJ,KAAA;IACA,KAAA;IAAW,KACP;IAAY,KACZ;IAAiB,KACjB;EAAO;EACK,QAChB;IACA,IAAA;IAAW,KACP;IACJ,KAAA;IACA,KAAA;IAAM,KACF;IAAY,KACZ;IACJ,KAAA;IACA,KAAA;IAAW,KACP;IAAY,KACZ;IAAoB,KACpB;EAAkB;EACK,QACvB;IAAuB,IACvB;IAAa,KACb;IAAkB,KAClB;IAAkB,KAClB;IAAoB,KACpB;IAAO,KACP;IAAe,KACf;IAAkB,KAClB;IAAyB,KACzB;IAAiB,KACjB;IAAsB,KACtB;EAAuB;EACC,SACxB;IAAW,IACX;IACJ,KAAA;IACA,KAAA;IAAM,KACF;IAAO,KACP;IAAY,KACZ;IAAY,KACZ;IACJ,KAAA;IACA,KAAA;IAAS,KACL;IAAY,KACZ;EAAiB;EACJ,MACb;IAAO,IACP;IACJ,KAAA;IACA,KAAA;IAAS,KACL;IAAQ,KACJ;IAAY,KACZ;IAAa,KACb;IAAO,KACX;IAAA,KACA;IAAS,KACL;IAAY,KACZ;EAAa;EACN,MACX;IAAA,IACA;IAAO,KACH;IAAY,KACZ;IAAa,KACb;IAAO,KACX;IACJ,KAAA;IACA,KAAA;IAAM,KACF;IAAQ,KACJ;IAAiB,KACjB;IAAoB,KACpB;EAAyB;EAClB,OACP;IAAY,IACZ;IAAe,KACf;IAAoB,KACpB;IAAM,KAAA;IACK,KAAA;IACK,KAChB;IAAA,KACJ;IAAA,KACA;IAAa,KACT;IAAY,KACZ;IAAO,KACX;EAAA;EACJ,MACA;IAAY,IACR;IAAM,KACF;IAAiB,KACjB;IAAkB,KAClB;IAAO,KACP;IAAY,KACZ;IAAa,KACb;IAAM,KAAA;IACK,KAAA;IACK,KAAA;IACC,KACjB;EAAA;EACJ,MACA;IAAc,IACV;IAAY,KACZ;IAAO,KACX;IAAA,KACA;IAAa,KACT;IAAO,KACP;IAAY,KACZ;IAAa,KACjB;IACJ,KAAA;IACJ,KAAA;IACA,KAAM;EAAA;EACO,SACF;IAAA,IACH;IAAI,KACJ;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;IAAK,KACL;EAAK;EACT,OACA;IAAS,IACL;IAAO,KACP;IAAe,KACf;IAAY,KACZ;IACJ,KAAA;IACA,KAAA;IAAW,KACP;IAAY,KACZ;IAAiB,KACjB;IAAO,KACP;IACJ,KAAA;EAAA;AACW;IACC,IACZ;EAAA,oBACM;EAAA,WACF;IAAY,OACZ;IACJ,OAAA;IACA,OAAA;IAAW,QACP;EAAY;EACQ,iBACpB;EAAkB,UAClB;EAAuB,cACvB;EAAuB,SACvB;IAAa,IACb;IAAkB,KAClB;IAAkB,KAClB;IAAoB,KACpB;IAAO,KACP;IAAe,KACf;IAAkB,KAClB;IAAyB,KACzB;IAAiB,KACjB;IAAsB,KACtB;IAAuB,KACvB;EAAwB;EACb,WACX;IACJ,UAAA;IACA,UAAM;IAAA,IACF;MACA,UAAA;MACA,UAAA;MACA,UAAA;IACJ;IACA,IAAA;MACI,UAAA;MACA,UAAA;MACA,UAAA;IAAa;IACN,cACK;IAChB,WAAA;MACA,OAAS;MACL,OAAA;MAAQ,OACJ;MAAY,QACZ;MAAa,QACb;IAAO;IACX,oBACS;EAAA;EACO,MACZ;IAAa,SACb;IAAO,KACX;IAAA,QACA;MAAO,SACH;IAAY;IACC,QACb;MACJ,SAAA;MACJ,cAAA;IACA;IAAM,aACM;MAAA,SACJ;MAAiB,YACjB;IAAoB;EACK;EAClB,SACP;IAAY,cACZ;EAAe;EACK,MACpB;IAAM,oBACK;EAAA;EACK,YAChB;IAAA,MACJ;MACA,SAAA;MAAa,KACT;IAAY;IACL,MACX;MACJ,SAAA;MACA,cAAY;MACR,KAAA;IAAM;IACe,cACjB;MAAkB,SACX;MAAA,YACP;IAAY;IACC,aACP;MAAA,MACF;IAAO;EACK;EACC,SACjB;IAAA,QACJ;MACA,cAAc;MAAA,QACV;IAAY;IACL,SACX;MACA,cAAa;MAAA,SACF;MAAA,QACP;IAAY;IACC,OACjB;MACJ,cAAA;MACJ,SAAA;MACJ,QAAA;IACJ;IAEO,YAAA;MACH,QAAA;IACA;EACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": []}