{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/dialog/index.ts"], "sourcesContent": ["import type { DialogDesignTokens, DialogTokenSections } from '@primeuix/themes/types/dialog';\n\nexport const root: DialogTokenSections.Root = {\n    background: '{overlay.modal.background}',\n    borderColor: '{overlay.modal.border.color}',\n    color: '{overlay.modal.color}',\n    borderRadius: '{overlay.modal.border.radius}',\n    shadow: '{overlay.modal.shadow}'\n};\n\nexport const header: DialogTokenSections.Header = {\n    padding: '{overlay.modal.padding}',\n    gap: '0.5rem'\n};\n\nexport const title: DialogTokenSections.Title = {\n    fontSize: '1.25rem',\n    fontWeight: '600'\n};\n\nexport const content: DialogTokenSections.Content = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}'\n};\n\nexport const footer: DialogTokenSections.Footer = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}',\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    header,\n    title,\n    content,\n    footer\n} satisfies DialogDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAiC;EAC1C,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;AACA;IACJ,IAAA;EAEa,SAAqC;EAC9C,KAAA;AACA;IACJ,IAAA;EAEa,UAAmC;EAC5C,YAAU;AACV;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,SAAqC;EAC9C,KAAA;AACA;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;AACA;", "names": []}