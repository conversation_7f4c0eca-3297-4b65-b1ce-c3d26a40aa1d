import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/inputchips/index.mjs
var o = {
  background: "{form.field.background}",
  disabledBackground: "{form.field.disabled.background}",
  filledBackground: "{form.field.filled.background}",
  filledFocusBackground: "{form.field.filled.focus.background}",
  borderColor: "{form.field.border.color}",
  hoverBorderColor: "{form.field.hover.border.color}",
  focusBorderColor: "{form.field.focus.border.color}",
  invalidBorderColor: "{form.field.invalid.border.color}",
  color: "{form.field.color}",
  disabledColor: "{form.field.disabled.color}",
  placeholderColor: "{form.field.placeholder.color}",
  shadow: "{form.field.shadow}",
  paddingX: "{form.field.padding.x}",
  paddingY: "{form.field.padding.y}",
  borderRadius: "{form.field.border.radius}",
  focusRing: {
    width: "{form.field.focus.ring.width}",
    style: "{form.field.focus.ring.style}",
    color: "{form.field.focus.ring.color}",
    offset: "{form.field.focus.ring.offset}",
    shadow: "{form.field.focus.ring.shadow}"
  },
  transitionDuration: "{form.field.transition.duration}"
};
var r = {
  borderRadius: "{border.radius.sm}"
};
var d = {
  light: {
    chip: {
      focusBackground: "{surface.200}",
      color: "{surface.800}"
    }
  },
  dark: {
    chip: {
      focusBackground: "{surface.700}",
      color: "{surface.0}"
    }
  }
};
var f = {
  root: o,
  chip: r,
  colorScheme: d
};
export {
  r as chip,
  d as colorScheme,
  f as default,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_inputchips.js.map
