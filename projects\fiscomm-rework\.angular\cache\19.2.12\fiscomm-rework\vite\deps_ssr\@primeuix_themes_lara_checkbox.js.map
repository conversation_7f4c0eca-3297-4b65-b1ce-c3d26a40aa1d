{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/checkbox/index.ts"], "sourcesContent": ["import type { CheckboxDesignTokens, CheckboxTokenSections } from '@primeuix/themes/types/checkbox';\n\nexport const root: CheckboxTokenSections.Root = {\n    borderRadius: '{border.radius.sm}',\n    width: '1.5rem',\n    height: '1.5rem',\n    background: '{form.field.background}',\n    checkedBackground: '{primary.color}',\n    checkedHoverBackground: '{primary.hover.color}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    checkedBorderColor: '{primary.color}',\n    checkedHoverBorderColor: '{primary.hover.color}',\n    checkedFocusBorderColor: '{primary.color}',\n    checkedDisabledBorderColor: '{form.field.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    shadow: '{form.field.shadow}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        width: '1.25rem',\n        height: '1.25rem'\n    },\n    lg: {\n        width: '1.75rem',\n        height: '1.75rem'\n    }\n};\n\nexport const icon: CheckboxTokenSections.Icon = {\n    size: '1rem',\n    color: '{form.field.color}',\n    checkedColor: '{primary.contrast.color}',\n    checkedHoverColor: '{primary.contrast.color}',\n    disabledColor: '{form.field.disabled.color}',\n    sm: {\n        size: '0.75rem'\n    },\n    lg: {\n        size: '1.25rem'\n    }\n};\n\nexport default {\n    root,\n    icon\n} satisfies CheckboxDesignTokens;\n"], "mappings": ";;;;;EAEa,cAAmC;EAC5C,OAAA;EACA,QAAO;EACP,YAAQ;EACR,mBAAY;EACZ,wBAAmB;EACnB,oBAAA;EACA,kBAAoB;EACpB,aAAA;EACA,kBAAa;EACb,kBAAkB;EAClB,oBAAkB;EAClB,yBAAoB;EACpB,yBAAyB;EACzB,4BAAyB;EACzB,oBAAA;EACA,QAAA;EACA,WAAQ;IACR,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;EACA,IAAA;IACI,OAAA;IACA,QAAO;EACP;EACJ,IAAA;IACI,OAAA;IACA,QAAO;EACP;AACJ;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;EACN,cAAO;EACP,mBAAc;EACd,eAAA;EACA,IAAA;IACI,MAAA;EACA;EACJ,IAAA;IACI,MAAA;EACA;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;AACA;", "names": []}