{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inputotp/index.ts"], "sourcesContent": ["import type { InputOtpDesignTokens, InputOtpTokenSections } from '@primeuix/themes/types/inputotp';\n\nexport const root: InputOtpTokenSections.Root = {\n    gap: '0.5rem'\n};\n\nexport const input: InputOtpTokenSections.Input = {\n    width: '2.5rem',\n    sm: {\n        width: '2rem'\n    },\n    lg: {\n        width: '3rem'\n    }\n};\n\nexport default {\n    root,\n    input\n} satisfies InputOtpDesignTokens;\n"], "mappings": ";;;;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,OAAA;EACT,IAAA;IACI,OAAA;EACA;EACJ,IAAA;IACI,OAAA;EACA;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;AACA;", "names": []}