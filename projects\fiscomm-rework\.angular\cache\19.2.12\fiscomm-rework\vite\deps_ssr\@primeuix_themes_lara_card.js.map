{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/card/index.ts"], "sourcesContent": ["import type { CardDesignTokens, CardTokenSections } from '@primeuix/themes/types/card';\n\nexport const root: CardTokenSections.Root = {\n    background: '{content.background}',\n    borderRadius: '{border.radius.lg}',\n    color: '{content.color}',\n    shadow: '0 .125rem .25rem rgba(0,0,0,.075)'\n};\n\nexport const body: CardTokenSections.Body = {\n    padding: '1.5rem',\n    gap: '0.75rem'\n};\n\nexport const caption: CardTokenSections.Caption = {\n    gap: '0.5rem'\n};\n\nexport const title: CardTokenSections.Title = {\n    fontSize: '1.25rem',\n    fontWeight: '700'\n};\n\nexport const subtitle: CardTokenSections.Subtitle = {\n    color: '{text.muted.color}'\n};\n\nexport default {\n    root,\n    body,\n    caption,\n    title,\n    subtitle\n} satisfies CardDesignTokens;\n"], "mappings": ";;;;;EAEa,YAA+B;EACxC,cAAY;EACZ,OAAA;EACA,QAAO;AACP;IACJ,IAAA;EAEa,SAA+B;EACxC,KAAA;AACA;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,UAAiC;EAC1C,YAAU;AACV;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,SAAA;EACA,OAAA;EACA,UAAA;AACA;", "names": []}