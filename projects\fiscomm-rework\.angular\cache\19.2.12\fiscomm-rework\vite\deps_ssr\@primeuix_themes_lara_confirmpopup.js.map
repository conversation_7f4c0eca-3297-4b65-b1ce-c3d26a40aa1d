{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/confirmpopup/index.ts"], "sourcesContent": ["import type { ConfirmPopupDesignTokens, ConfirmPopupTokenSections } from '@primeuix/themes/types/confirmpopup';\n\nexport const root: ConfirmPopupTokenSections.Root = {\n    background: '{overlay.popover.background}',\n    borderColor: '{overlay.popover.border.color}',\n    color: '{overlay.popover.color}',\n    borderRadius: '{overlay.popover.border.radius}',\n    shadow: '{overlay.popover.shadow}',\n    gutter: '10px',\n    arrowOffset: '1.25rem'\n};\n\nexport const content: ConfirmPopupTokenSections.Content = {\n    padding: '{overlay.popover.padding}',\n    gap: '1rem'\n};\n\nexport const icon: ConfirmPopupTokenSections.Icon = {\n    size: '1.5rem',\n    color: '{overlay.popover.color}'\n};\n\nexport const footer: ConfirmPopupTokenSections.Footer = {\n    gap: '0.5rem',\n    padding: '0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}'\n};\n\nexport default {\n    root,\n    content,\n    icon,\n    footer\n} satisfies ConfirmPopupDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAuC;EAChD,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;EACA,QAAQ;EACR,aAAQ;AACR;IACJ,IAAA;EAEa,SAAA;EACT,KAAA;AACA;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;AACN;IACJ,IAAA;EAEa,KAAA;EACT,SAAK;AACL;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,MAAA;EACA,QAAA;AACA;", "names": []}