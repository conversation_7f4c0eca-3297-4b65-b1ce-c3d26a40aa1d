{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tabmenu/index.ts"], "sourcesContent": ["import type { TabmenuDesignTokens, TabmenuTokenSections } from '@primeuix/themes/types/tabmenu';\n\nexport const root: TabmenuTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const tablist: TabmenuTokenSections.Tablist = {\n    borderWidth: '0',\n    background: '{content.background}',\n    borderColor: '{content.border.color}'\n};\n\nexport const item: TabmenuTokenSections.Item = {\n    borderWidth: '2px 0 0 0',\n    borderColor: 'transparent',\n    hoverBorderColor: 'transparent',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}',\n    padding: '1rem 1.25rem',\n    fontWeight: '600',\n    margin: '0',\n    gap: '0.5rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const itemIcon: TabmenuTokenSections.ItemIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}'\n};\n\nexport const activeBar: TabmenuTokenSections.ActiveBar = {\n    height: '0',\n    bottom: '0',\n    background: 'transparent'\n};\n\nexport const colorScheme: TabmenuTokenSections.ColorScheme = {\n    light: {\n        item: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            activeBackground: '{surface.0}'\n        }\n    },\n    dark: {\n        item: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    tablist,\n    item,\n    itemIcon,\n    activeBar,\n    colorScheme\n} satisfies TabmenuDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAkC;AAC3C;IACJ,IAAA;EAEa,aAAwC;EACjD,YAAa;EACb,aAAY;AACZ;IACJ,IAAA;EAEa,aAAkC;EAC3C,aAAa;EACb,kBAAa;EACb,mBAAkB;EAClB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,YAAS;EACT,QAAA;EACA,KAAA;EACA,WAAK;IACL,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,QAAA;EACT,QAAQ;EACR,YAAQ;AACR;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,iBAAY;MACZ,kBAAiB;IACjB;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,iBAAY;MACZ,kBAAiB;IACjB;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,MAAA;EACA,UAAA;EACA,WAAA;EACA,aAAA;AACA;", "names": []}