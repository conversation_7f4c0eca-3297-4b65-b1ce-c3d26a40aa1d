{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/image/index.ts"], "sourcesContent": ["import type { ImageDesignTokens, ImageTokenSections } from '@primeuix/themes/types/image';\n\nexport const root: ImageTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const preview: ImageTokenSections.Preview = {\n    icon: {\n        size: '1.5rem'\n    },\n    mask: {\n        background: '{mask.background}',\n        color: '{mask.color}'\n    }\n};\n\nexport const toolbar: ImageTokenSections.Toolbar = {\n    position: {\n        left: 'auto',\n        right: '1rem',\n        top: '1rem',\n        bottom: 'auto'\n    },\n    blur: '8px',\n    background: 'rgba(255,255,255,0.1)',\n    borderColor: 'rgba(255,255,255,0.2)',\n    borderWidth: '1px',\n    borderRadius: '{content.border.radius}',\n    padding: '.5rem',\n    gap: '0.5rem'\n};\n\nexport const action: ImageTokenSections.Action = {\n    hoverBackground: 'rgba(255,255,255,0.1)',\n    color: '{surface.50}',\n    hoverColor: '{surface.0}',\n    size: '3rem',\n    iconSize: '1.5rem',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport default {\n    root,\n    preview,\n    toolbar,\n    action\n} satisfies ImageDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAgC;AACzC;IACJ,IAAA;EAEa,MAAA;IACT,MAAM;EACF;EACJ,MAAA;IACA,YAAM;IACF,OAAA;EACA;AACJ;IACJ,IAAA;EAEa,UAAsC;IAC/C,MAAU;IACN,OAAM;IACN,KAAO;IACP,QAAK;EACL;EACJ,MAAA;EACA,YAAM;EACN,aAAY;EACZ,aAAa;EACb,cAAa;EACb,SAAA;EACA,KAAA;AACA;IACJ,IAAA;EAEa,iBAAoC;EAC7C,OAAA;EACA,YAAO;EACP,MAAA;EACA,UAAM;EACN,cAAU;EACV,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,SAAA;EACA,QAAA;AACA;", "names": []}