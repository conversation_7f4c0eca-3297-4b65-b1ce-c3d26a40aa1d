{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/panelmenu/index.ts"], "sourcesContent": ["import type { PanelMenuDesignTokens, PanelMenuTokenSections } from '@primeuix/themes/types/panelmenu';\n\nexport const root: PanelMenuTokenSections.Root = {\n    gap: '0',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const panel: PanelMenuTokenSections.Panel = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderWidth: '1px',\n    color: '{content.color}',\n    padding: '0.25rem 0.25rem',\n    borderRadius: '0',\n    first: {\n        borderWidth: '1px 1px 0 1px',\n        topBorderRadius: '{content.border.radius}'\n    },\n    last: {\n        borderWidth: '0 1px 1px 1px',\n        bottomBorderRadius: '{content.border.radius}'\n    }\n};\n\nexport const item: PanelMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    gap: '0.5rem',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{content.border.radius}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}'\n    }\n};\n\nexport const submenu: PanelMenuTokenSections.Submenu = {\n    indent: '1rem'\n};\n\nexport const submenuIcon: PanelMenuTokenSections.SubmenuIcon = {\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}'\n};\n\nexport default {\n    root,\n    panel,\n    item,\n    submenu,\n    submenuIcon\n} satisfies PanelMenuDesignTokens;\n"], "mappings": ";;;;EAEa,KAAA;EACT,oBAAK;AACL;IACJ,IAAA;EAEa,YAAsC;EAC/C,aAAY;EACZ,aAAa;EACb,OAAA;EACA,SAAO;EACP,cAAS;EACT,OAAA;IACA,aAAO;IACH,iBAAa;EACb;EACJ,MAAA;IACA,aAAM;IACF,oBAAa;EACb;AACJ;IACJ,IAAA;EAEa,iBAAoC;EAC7C,OAAA;EACA,YAAO;EACP,KAAA;EACA,SAAK;EACL,cAAS;EACT,MAAA;IACA,OAAM;IACF,YAAO;EACP;AACJ;IACJ,IAAA;EAEa,QAAA;AACT;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;AACP;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,MAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}