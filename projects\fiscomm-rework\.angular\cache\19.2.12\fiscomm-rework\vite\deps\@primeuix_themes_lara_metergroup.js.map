{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/metergroup/index.ts"], "sourcesContent": ["import type { MeterGroupDesignTokens, MeterGroupTokenSections } from '@primeuix/themes/types/metergroup';\n\nexport const root: MeterGroupTokenSections.Root = {\n    borderRadius: '{content.border.radius}',\n    gap: '1rem'\n};\n\nexport const meters: MeterGroupTokenSections.Meters = {\n    background: '{content.border.color}',\n    size: '0.625rem'\n};\n\nexport const label: MeterGroupTokenSections.Label = {\n    gap: '0.5rem'\n};\n\nexport const labelMarker: MeterGroupTokenSections.LabelMarker = {\n    size: '0.5rem'\n};\n\nexport const labelIcon: MeterGroupTokenSections.LabelIcon = {\n    size: '1rem'\n};\n\nexport const labelList: MeterGroupTokenSections.LabelList = {\n    verticalGap: '0.5rem',\n    horizontalGap: '1rem'\n};\n\nexport default {\n    root,\n    meters,\n    label,\n    labelMarker,\n    labelIcon,\n    labelList\n} satisfies MeterGroupDesignTokens;\n"], "mappings": ";;;;EAEa,cAAqC;EAC9C,KAAA;AACA;IACJ,IAAA;EAEa,YAAyC;EAClD,MAAA;AACA;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,aAA+C;EACxD,eAAa;AACb;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,OAAA;EACA,aAAA;EACA,WAAA;EACA,WAAA;AACA;", "names": []}