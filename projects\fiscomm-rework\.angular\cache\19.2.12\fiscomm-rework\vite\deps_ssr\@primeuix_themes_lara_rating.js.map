{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/rating/index.ts"], "sourcesContent": ["import type { RatingDesignTokens, RatingTokenSections } from '@primeuix/themes/types/rating';\n\nexport const root: RatingTokenSections.Root = {\n    gap: '0.25rem',\n    transitionDuration: '{transition.duration}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const icon: RatingTokenSections.Icon = {\n    size: '1.25rem',\n    color: '{text.muted.color}',\n    hoverColor: '{primary.color}',\n    activeColor: '{primary.color}'\n};\n\nexport default {\n    root,\n    icon\n} satisfies RatingDesignTokens;\n"], "mappings": ";;;;;EAEa,KAAA;EACT,oBAAK;EACL,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;EACN,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;AACA;", "names": []}