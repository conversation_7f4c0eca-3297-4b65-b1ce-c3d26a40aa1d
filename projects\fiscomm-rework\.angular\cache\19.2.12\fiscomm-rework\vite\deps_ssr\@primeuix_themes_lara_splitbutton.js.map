{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/splitbutton/index.ts"], "sourcesContent": ["import type { SplitButtonDesignTokens, SplitButtonTokenSections } from '@primeuix/themes/types/splitbutton';\n\nexport const root: SplitButtonTokenSections.Root = {\n    borderRadius: '{form.field.border.radius}',\n    roundedBorderRadius: '2rem',\n    raisedShadow: '0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)'\n};\n\nexport default {\n    root\n} satisfies SplitButtonDesignTokens;\n"], "mappings": ";;;;;EAEa,cAAsC;EAC/C,qBAAc;EACd,cAAA;AACA;IACJ,IAAA;EAEO,MAAA;AACH;", "names": []}