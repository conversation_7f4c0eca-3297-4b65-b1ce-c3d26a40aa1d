{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/menu/index.ts"], "sourcesContent": ["import type { MenuDesignTokens, MenuTokenSections } from '@primeuix/themes/types/menu';\n\nexport const root: MenuTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const list: MenuTokenSections.List = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const item: MenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}'\n    }\n};\n\nexport const submenuLabel: MenuTokenSections.SubmenuLabel = {\n    padding: '{navigation.submenu.label.padding}',\n    fontWeight: '{navigation.submenu.label.font.weight}',\n    background: '{navigation.submenu.label.background.}',\n    color: '{navigation.submenu.label.color}'\n};\n\nexport const separator: MenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport default {\n    root,\n    list,\n    item,\n    submenuLabel,\n    separator\n} satisfies MenuDesignTokens;\n"], "mappings": ";;;;;EAEa,YAA+B;EACxC,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;EACA,oBAAQ;AACR;IACJ,IAAA;EAEa,SAA+B;EACxC,KAAA;AACA;IACJ,IAAA;EAEa,iBAA+B;EACxC,OAAA;EACA,YAAO;EACP,SAAA;EACA,cAAS;EACT,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;EACP;AACJ;IACJ,IAAA;EAEa,SAAA;EACT,YAAS;EACT,YAAY;EACZ,OAAA;AACA;IACJ,IAAA;EAEa,aAAyC;AAClD;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,MAAA;EACA,cAAA;EACA,WAAA;AACA;", "names": []}