import type { OrganizationChartTokenSections } from '@primeuix/themes/types/organizationchart';

export * from '@primeuix/themes/types/organizationchart';

declare const root: OrganizationChartTokenSections.Root;
declare const node: OrganizationChartTokenSections.Node;
declare const nodeToggleButton: OrganizationChartTokenSections.NodeToggleButton;
declare const connector: OrganizationChartTokenSections.Connector;
declare const _default: {
    root: OrganizationChartTokenSections.Root;
    node: OrganizationChartTokenSections.Node;
    nodeToggleButton: OrganizationChartTokenSections.NodeToggleButton;
    connector: OrganizationChartTokenSections.Connector;
};

export { connector, _default as default, node, nodeToggleButton, root };
