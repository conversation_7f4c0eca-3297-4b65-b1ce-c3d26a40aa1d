{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/paginator/index.ts"], "sourcesContent": ["import type { PaginatorDesignTokens, PaginatorTokenSections } from '@primeuix/themes/types/paginator';\n\nexport const root: PaginatorTokenSections.Root = {\n    padding: '0.5rem 1rem',\n    gap: '0.25rem',\n    borderRadius: '{content.border.radius}',\n    background: '{content.background}',\n    color: '{content.color}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const navButton: PaginatorTokenSections.NavButton = {\n    background: 'transparent',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    selectedColor: '{highlight.color}',\n    width: '2.5rem',\n    height: '2.5rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const currentPageReport: PaginatorTokenSections.CurrentPageReport = {\n    color: '{text.muted.color}'\n};\n\nexport const jumpToPageInput: PaginatorTokenSections.JumpToPageInput = {\n    maxWidth: '2.5rem'\n};\n\nexport default {\n    root,\n    navButton,\n    currentPageReport,\n    jumpToPageInput\n} satisfies PaginatorDesignTokens;\n"], "mappings": ";;;;;EAEa,SAAoC;EAC7C,KAAA;EACA,cAAK;EACL,YAAc;EACd,OAAA;EACA,oBAAO;AACP;IACJ,IAAA;EAEa,YAA8C;EACvD,iBAAY;EACZ,oBAAiB;EACjB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,OAAA;EACA,QAAO;EACP,cAAQ;EACR,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEa,UAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,WAAA;EACA,mBAAA;EACA,iBAAA;AACA;", "names": []}