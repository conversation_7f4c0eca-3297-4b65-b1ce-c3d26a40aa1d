{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/confirmdialog/index.ts"], "sourcesContent": ["import type { ConfirmDialogDesignTokens, ConfirmDialogTokenSections } from '@primeuix/themes/types/confirmdialog';\n\nexport const icon: ConfirmDialogTokenSections.Icon = {\n    size: '2rem',\n    color: '{overlay.modal.color}'\n};\n\nexport const content: ConfirmDialogTokenSections.Content = {\n    gap: '1rem'\n};\n\nexport default {\n    icon,\n    content\n} satisfies ConfirmDialogDesignTokens;\n"], "mappings": ";;;;;EAEa,MAAA;EACT,OAAM;AACN;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;AACA;", "names": []}