import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/carousel/index.mjs
var r = {
  transitionDuration: "{transition.duration}"
};
var o = {
  gap: "0.25rem"
};
var a = {
  padding: "1rem",
  gap: "0.5rem"
};
var i = {
  width: "1rem",
  height: "1rem",
  borderRadius: "50",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var c = {
  light: {
    indicator: {
      background: "{surface.200}",
      hoverBackground: "{surface.300}",
      activeBackground: "{primary.color}"
    }
  },
  dark: {
    indicator: {
      background: "{surface.700}",
      hoverBackground: "{surface.600}",
      activeBackground: "{primary.color}"
    }
  }
};
var t = {
  root: r,
  content: o,
  indicatorList: a,
  indicator: i,
  colorScheme: c
};
export {
  c as colorScheme,
  o as content,
  t as default,
  i as indicator,
  a as indicatorList,
  r as root
};
//# sourceMappingURL=@primeuix_themes_lara_carousel.js.map
