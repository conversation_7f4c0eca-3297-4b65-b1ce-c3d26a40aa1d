{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/terminal/index.ts"], "sourcesContent": ["import type { TerminalDesignTokens, TerminalTokenSections } from '@primeuix/themes/types/terminal';\n\nexport const root: TerminalTokenSections.Root = {\n    background: '{form.field.background}',\n    borderColor: '{form.field.border.color}',\n    color: '{form.field.color}',\n    height: '18rem',\n    padding: '{form.field.padding.y} {form.field.padding.x}',\n    borderRadius: '{form.field.border.radius}'\n};\n\nexport const prompt: TerminalTokenSections.Prompt = {\n    gap: '0.25rem'\n};\n\nexport const commandResponse: TerminalTokenSections.CommandResponse = {\n    margin: '2px 0'\n};\n\nexport default {\n    root,\n    prompt,\n    commandResponse\n} satisfies TerminalDesignTokens;\n"], "mappings": ";;;;EAEa,YAAmC;EAC5C,aAAY;EACZ,OAAA;EACA,QAAO;EACP,SAAQ;EACR,cAAS;AACT;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,QAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,iBAAA;AACA;", "names": []}