{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/galleria/index.ts"], "sourcesContent": ["import type { GalleriaDesignTokens, GalleriaTokenSections } from '@primeuix/themes/types/galleria';\n\nexport const root: GalleriaTokenSections.Root = {\n    borderWidth: '1px',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const navButton: GalleriaTokenSections.NavButton = {\n    background: 'rgba(255, 255, 255, 0.1)',\n    hoverBackground: 'rgba(255, 255, 255, 0.2)',\n    color: '{surface.100}',\n    hoverColor: '{surface.0}',\n    size: '3rem',\n    gutter: '0',\n    prev: {\n        borderRadius: '0 12px 12px 0'\n    },\n    next: {\n        borderRadius: '12px 0 0 12px'\n    },\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const navIcon: GalleriaTokenSections.NavIcon = {\n    size: '1.5rem'\n};\n\nexport const thumbnailsContent: GalleriaTokenSections.ThumbnailsContent = {\n    padding: '1rem 0.25rem'\n};\n\nexport const thumbnailNavButton: GalleriaTokenSections.ThumbnailNavButton = {\n    size: '2rem',\n    borderRadius: '{content.border.radius}',\n    gutter: '0.5rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const thumbnailNavButtonIcon: GalleriaTokenSections.ThumbnailNavButtonIcon = {\n    size: '1rem'\n};\n\nexport const caption: GalleriaTokenSections.Caption = {\n    background: 'rgba(0, 0, 0, 0.5)',\n    color: '{surface.100}',\n    padding: '1rem'\n};\n\nexport const indicatorList: GalleriaTokenSections.IndicatorList = {\n    gap: '0.5rem',\n    padding: '1rem'\n};\n\nexport const indicatorButton: GalleriaTokenSections.IndicatorButton = {\n    width: '1rem',\n    height: '1rem',\n    activeBackground: '{primary.color}',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const insetIndicatorList: GalleriaTokenSections.InsetIndicatorList = {\n    background: 'rgba(0, 0, 0, 0.5)'\n};\n\nexport const insetIndicatorButton: GalleriaTokenSections.InsetIndicatorButton = {\n    background: 'rgba(255, 255, 255, 0.4)',\n    hoverBackground: 'rgba(255, 255, 255, 0.6)',\n    activeBackground: 'rgba(255, 255, 255, 0.9)'\n};\n\nexport const closeButton: GalleriaTokenSections.CloseButton = {\n    size: '3rem',\n    gutter: '0.5rem',\n    background: 'rgba(255, 255, 255, 0.1)',\n    hoverBackground: 'rgba(255, 255, 255, 0.2)',\n    color: '{surface.50}',\n    hoverColor: '{surface.0}',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const closeButtonIcon: GalleriaTokenSections.CloseButtonIcon = {\n    size: '1.5rem'\n};\n\nexport const colorScheme: GalleriaTokenSections.ColorScheme = {\n    light: {\n        thumbnailsContent: {\n            background: '{surface.50}'\n        },\n        thumbnailNavButton: {\n            hoverBackground: '{surface.100}',\n            color: '{surface.600}',\n            hoverColor: '{surface.700}'\n        },\n        indicatorButton: {\n            background: '{surface.200}',\n            hoverBackground: '{surface.300}'\n        }\n    },\n    dark: {\n        thumbnailsContent: {\n            background: '{surface.800}'\n        },\n        thumbnailNavButton: {\n            hoverBackground: '{surface.700}',\n            color: '{surface.400}',\n            hoverColor: '{surface.0}'\n        },\n        indicatorButton: {\n            background: '{surface.700}',\n            hoverBackground: '{surface.600}'\n        }\n    }\n};\n\nexport default {\n    root,\n    navButton,\n    navIcon,\n    thumbnailsContent,\n    thumbnailNavButton,\n    thumbnailNavButtonIcon,\n    caption,\n    indicatorList,\n    indicatorButton,\n    insetIndicatorList,\n    insetIndicatorButton,\n    closeButton,\n    closeButtonIcon,\n    colorScheme\n} satisfies GalleriaDesignTokens;\n"], "mappings": ";;;;EAEa,aAAmC;EAC5C,aAAa;EACb,cAAa;EACb,oBAAc;AACd;IACJ,IAAA;EAEa,YAA6C;EACtD,iBAAY;EACZ,OAAA;EACA,YAAO;EACP,MAAA;EACA,QAAM;EACN,MAAQ;IACR,cAAM;EACF;EACJ,MAAA;IACA,cAAM;EACF;EACJ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,SAAA;AACT;IACJ,IAAA;EAEa,MAAA;EACT,cAAM;EACN,QAAA;EACA,WAAQ;IACR,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,YAAyC;EAClD,OAAA;EACA,SAAO;AACP;IACJ,IAAA;EAEa,KAAA;EACT,SAAK;AACL;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,kBAAQ;EACR,cAAA;EACA,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,YAAA;AACT;IACJ,IAAA;EAEa,YAAA;EACT,iBAAY;EACZ,kBAAiB;AACjB;IACJ,IAAA;EAEa,MAAA;EACT,QAAM;EACN,YAAQ;EACR,iBAAY;EACZ,OAAA;EACA,YAAO;EACP,cAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,mBAAO;MACH,YAAA;IACI;IACJ,oBAAA;MACA,iBAAoB;MAChB,OAAA;MACA,YAAO;IACP;IACJ,iBAAA;MACA,YAAA;MACI,iBAAY;IACZ;EACJ;EACJ,MAAA;IACA,mBAAM;MACF,YAAA;IACI;IACJ,oBAAA;MACA,iBAAoB;MAChB,OAAA;MACA,YAAO;IACP;IACJ,iBAAA;MACA,YAAA;MACI,iBAAY;IACZ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,WAAA;EACA,SAAA;EACA,mBAAA;EACA,oBAAA;EACA,wBAAA;EACA,SAAA;EACA,eAAA;EACA,iBAAA;EACA,oBAAA;EACA,sBAAA;EACA,aAAA;EACA,iBAAA;EACA,aAAA;AACA;", "names": []}