{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inputtext/index.ts"], "sourcesContent": ["import type { InputTextDesignTokens, InputTextTokenSections } from '@primeuix/themes/types/inputtext';\n\nexport const root: InputTextTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    filledHoverBackground: '{form.field.filled.hover.background}',\n    filledFocusBackground: '{form.field.filled.focus.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    placeholderColor: '{form.field.placeholder.color}',\n    invalidPlaceholderColor: '{form.field.invalid.placeholder.color}',\n    shadow: '{form.field.shadow}',\n    paddingX: '{form.field.padding.x}',\n    paddingY: '{form.field.padding.y}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        fontSize: '{form.field.sm.font.size}',\n        paddingX: '{form.field.sm.padding.x}',\n        paddingY: '{form.field.sm.padding.y}'\n    },\n    lg: {\n        fontSize: '{form.field.lg.font.size}',\n        paddingX: '{form.field.lg.padding.x}',\n        paddingY: '{form.field.lg.padding.y}'\n    }\n};\n\nexport default {\n    root\n} satisfies InputTextDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAoC;EAC7C,oBAAY;EACZ,kBAAoB;EACpB,uBAAkB;EAClB,uBAAuB;EACvB,aAAA;EACA,kBAAa;EACb,kBAAkB;EAClB,oBAAkB;EAClB,OAAA;EACA,eAAO;EACP,kBAAe;EACf,yBAAkB;EAClB,QAAA;EACA,UAAQ;EACR,UAAU;EACV,cAAU;EACV,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;EACA,IAAA;IACI,UAAA;IACA,UAAU;IACV,UAAU;EACV;EACJ,IAAA;IACI,UAAA;IACA,UAAU;IACV,UAAU;EACV;AACJ;IACJ,IAAA;EAEO,MAAA;AACH;", "names": []}