import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/scrollpanel/index.mjs
var r = {
  transitionDuration: "{transition.duration}"
};
var o = {
  size: "9px",
  borderRadius: "{border.radius.sm}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var s = {
  light: {
    bar: {
      background: "{surface.200}"
    }
  },
  dark: {
    bar: {
      background: "{surface.700}"
    }
  }
};
var a = {
  root: r,
  bar: o,
  colorScheme: s
};
export {
  o as bar,
  s as colorScheme,
  a as default,
  r as root
};
//# sourceMappingURL=@primeuix_themes_lara_scrollpanel.js.map
