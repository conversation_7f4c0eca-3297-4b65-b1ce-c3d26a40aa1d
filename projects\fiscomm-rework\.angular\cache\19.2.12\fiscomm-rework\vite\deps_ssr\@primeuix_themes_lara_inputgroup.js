import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/inputgroup/index.mjs
var r = {
  borderRadius: "{form.field.border.radius}",
  padding: "0.625rem 0.5rem",
  minWidth: "2.75rem"
};
var o = {
  light: {
    addon: {
      background: "{surface.50}",
      borderColor: "{form.field.border.color}",
      color: "{text.muted.color}"
    }
  },
  dark: {
    addon: {
      background: "{surface.800}",
      borderColor: "{form.field.border.color}",
      color: "{text.muted.color}"
    }
  }
};
var d = {
  addon: r,
  colorScheme: o
};
export {
  r as addon,
  o as colorScheme,
  d as default
};
//# sourceMappingURL=@primeuix_themes_lara_inputgroup.js.map
