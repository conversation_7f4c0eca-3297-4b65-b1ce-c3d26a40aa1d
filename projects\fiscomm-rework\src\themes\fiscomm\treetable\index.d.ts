import type { TreeTableTokenSections } from '@primeuix/themes/types/treetable';

export * from '@primeuix/themes/types/treetable';

declare const root: TreeTableTokenSections.Root;
declare const header: TreeTableTokenSections.Header;
declare const headerCell: TreeTableTokenSections.HeaderCell;
declare const columnTitle: TreeTableTokenSections.ColumnTitle;
declare const row: TreeTableTokenSections.Row;
declare const bodyCell: TreeTableTokenSections.BodyCell;
declare const footerCell: TreeTableTokenSections.FooterCell;
declare const columnFooter: TreeTableTokenSections.ColumnFooter;
declare const footer: TreeTableTokenSections.Footer;
declare const columnResizer: TreeTableTokenSections.ColumnResizer;
declare const resizeIndicator: TreeTableTokenSections.ResizeIndicator;
declare const sortIcon: TreeTableTokenSections.SortIcon;
declare const loadingIcon: TreeTableTokenSections.LoadingIcon;
declare const nodeToggleButton: TreeTableTokenSections.NodeToggleButton;
declare const paginatorTop: TreeTableTokenSections.PaginatorTop;
declare const paginatorBottom: TreeTableTokenSections.PaginatorBottom;
declare const colorScheme: TreeTableTokenSections.ColorScheme;
declare const _default: {
    root: TreeTableTokenSections.Root;
    header: TreeTableTokenSections.Header;
    headerCell: TreeTableTokenSections.HeaderCell;
    columnTitle: TreeTableTokenSections.ColumnTitle;
    row: TreeTableTokenSections.Row;
    bodyCell: TreeTableTokenSections.BodyCell;
    footerCell: TreeTableTokenSections.FooterCell;
    columnFooter: TreeTableTokenSections.ColumnFooter;
    footer: TreeTableTokenSections.Footer;
    columnResizer: TreeTableTokenSections.ColumnResizer;
    resizeIndicator: TreeTableTokenSections.ResizeIndicator;
    sortIcon: TreeTableTokenSections.SortIcon;
    loadingIcon: TreeTableTokenSections.LoadingIcon;
    nodeToggleButton: TreeTableTokenSections.NodeToggleButton;
    paginatorTop: TreeTableTokenSections.PaginatorTop;
    paginatorBottom: TreeTableTokenSections.PaginatorBottom;
    colorScheme: TreeTableTokenSections.ColorScheme;
};

export { bodyCell, colorScheme, columnFooter, columnResizer, columnTitle, _default as default, footer, footerCell, header, headerCell, loadingIcon, nodeToggleButton, paginatorBottom, paginatorTop, resizeIndicator, root, row, sortIcon };
