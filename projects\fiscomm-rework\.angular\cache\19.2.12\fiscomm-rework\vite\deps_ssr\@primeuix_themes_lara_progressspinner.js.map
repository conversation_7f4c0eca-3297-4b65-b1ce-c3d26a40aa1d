{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/progressspinner/index.ts"], "sourcesContent": ["import type { ProgressSpinnerDesignTokens, ProgressSpinnerTokenSections } from '@primeuix/themes/types/progressspinner';\n\nexport const colorScheme: ProgressSpinnerTokenSections.ColorScheme = {\n    light: {\n        root: {\n            colorOne: '{pink.500}',\n            colorTwo: '{sky.500}',\n            colorThree: '{emerald.500}',\n            colorFour: '{amber.500}'\n        }\n    },\n    dark: {\n        root: {\n            colorOne: '{pink.400}',\n            colorTwo: '{sky.400}',\n            colorThree: '{emerald.400}',\n            colorFour: '{amber.400}'\n        }\n    }\n};\n\nexport default {\n    colorScheme\n} satisfies ProgressSpinnerDesignTokens;\n"], "mappings": ";;;;;EAEa,OAAA;IACT,MAAO;MACH,UAAM;MACF,UAAU;MACV,YAAU;MACV,WAAY;IACZ;EACJ;EACJ,MAAA;IACA,MAAM;MACF,UAAM;MACF,UAAU;MACV,YAAU;MACV,WAAY;IACZ;EACJ;AACJ;IACJ,IAAA;EAEO,aAAA;AACH;", "names": []}