import "./chunk-WDMUDEB6.js";

// node_modules/@primeuix/themes/lara/galleria/index.mjs
var o = {
  borderWidth: "1px",
  borderColor: "{content.border.color}",
  borderRadius: "{content.border.radius}",
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "rgba(255, 255, 255, 0.1)",
  hoverBackground: "rgba(255, 255, 255, 0.2)",
  color: "{surface.100}",
  hoverColor: "{surface.0}",
  size: "3rem",
  gutter: "0",
  prev: {
    borderRadius: "0 12px 12px 0"
  },
  next: {
    borderRadius: "12px 0 0 12px"
  },
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var e = {
  size: "1.5rem"
};
var t = {
  padding: "1rem 0.25rem"
};
var n = {
  size: "2rem",
  borderRadius: "{content.border.radius}",
  gutter: "0.5rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var a = {
  size: "1rem"
};
var c = {
  background: "rgba(0, 0, 0, 0.5)",
  color: "{surface.100}",
  padding: "1rem"
};
var s = {
  gap: "0.5rem",
  padding: "1rem"
};
var u = {
  width: "1rem",
  height: "1rem",
  activeBackground: "{primary.color}",
  borderRadius: "50%",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var i = {
  background: "rgba(0, 0, 0, 0.5)"
};
var d = {
  background: "rgba(255, 255, 255, 0.4)",
  hoverBackground: "rgba(255, 255, 255, 0.6)",
  activeBackground: "rgba(255, 255, 255, 0.9)"
};
var g = {
  size: "3rem",
  gutter: "0.5rem",
  background: "rgba(255, 255, 255, 0.1)",
  hoverBackground: "rgba(255, 255, 255, 0.2)",
  color: "{surface.50}",
  hoverColor: "{surface.0}",
  borderRadius: "50%",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var f = {
  size: "1.5rem"
};
var h = {
  light: {
    thumbnailsContent: {
      background: "{surface.50}"
    },
    thumbnailNavButton: {
      hoverBackground: "{surface.100}",
      color: "{surface.600}",
      hoverColor: "{surface.700}"
    },
    indicatorButton: {
      background: "{surface.200}",
      hoverBackground: "{surface.300}"
    }
  },
  dark: {
    thumbnailsContent: {
      background: "{surface.800}"
    },
    thumbnailNavButton: {
      hoverBackground: "{surface.700}",
      color: "{surface.400}",
      hoverColor: "{surface.0}"
    },
    indicatorButton: {
      background: "{surface.700}",
      hoverBackground: "{surface.600}"
    }
  }
};
var l = {
  root: o,
  navButton: r,
  navIcon: e,
  thumbnailsContent: t,
  thumbnailNavButton: n,
  thumbnailNavButtonIcon: a,
  caption: c,
  indicatorList: s,
  indicatorButton: u,
  insetIndicatorList: i,
  insetIndicatorButton: d,
  closeButton: g,
  closeButtonIcon: f,
  colorScheme: h
};
export {
  c as caption,
  g as closeButton,
  f as closeButtonIcon,
  h as colorScheme,
  l as default,
  u as indicatorButton,
  s as indicatorList,
  d as insetIndicatorButton,
  i as insetIndicatorList,
  r as navButton,
  e as navIcon,
  o as root,
  n as thumbnailNavButton,
  a as thumbnailNavButtonIcon,
  t as thumbnailsContent
};
//# sourceMappingURL=@primeuix_themes_lara_galleria.js.map
