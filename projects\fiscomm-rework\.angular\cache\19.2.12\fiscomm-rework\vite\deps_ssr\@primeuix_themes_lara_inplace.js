import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/inplace/index.mjs
var o = {
  padding: "{form.field.padding.y} {form.field.padding.x}",
  borderRadius: "{content.border.radius}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  },
  transitionDuration: "{transition.duration}"
};
var r = {
  hoverBackground: "{content.hover.background}",
  hoverColor: "{content.hover.color}"
};
var n = {
  root: o,
  display: r
};
export {
  n as default,
  r as display,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_inplace.js.map
