{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inputchips/index.ts"], "sourcesContent": ["import type { InputChipsDesignTokens, InputChipsTokenSections } from '@primeuix/themes/types/inputchips';\n\nexport const root: InputChipsTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    filledFocusBackground: '{form.field.filled.focus.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    placeholderColor: '{form.field.placeholder.color}',\n    shadow: '{form.field.shadow}',\n    paddingX: '{form.field.padding.x}',\n    paddingY: '{form.field.padding.y}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}'\n};\n\nexport const chip: InputChipsTokenSections.Chip = {\n    borderRadius: '{border.radius.sm}'\n};\n\nexport const colorScheme: InputChipsTokenSections.ColorScheme = {\n    light: {\n        chip: {\n            focusBackground: '{surface.200}',\n            color: '{surface.800}'\n        }\n    },\n    dark: {\n        chip: {\n            focusBackground: '{surface.700}',\n            color: '{surface.0}'\n        }\n    }\n};\n\nexport default {\n    root,\n    chip,\n    colorScheme\n} satisfies InputChipsDesignTokens;\n"], "mappings": ";;;;EAEa,YAAqC;EAC9C,oBAAY;EACZ,kBAAoB;EACpB,uBAAkB;EAClB,aAAA;EACA,kBAAa;EACb,kBAAkB;EAClB,oBAAkB;EAClB,OAAA;EACA,eAAO;EACP,kBAAe;EACf,QAAA;EACA,UAAQ;EACR,UAAU;EACV,cAAU;EACV,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;AACA;IACJ,IAAA;EAEa,cAAqC;AAC9C;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,iBAAM;MACF,OAAA;IACA;EACJ;EACJ,MAAA;IACA,MAAM;MACF,iBAAM;MACF,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,aAAA;AACA;", "names": []}