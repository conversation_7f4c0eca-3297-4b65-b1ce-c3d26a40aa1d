{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/colorpicker/index.ts"], "sourcesContent": ["import type { ColorPickerDesignTokens, ColorPickerTokenSections } from '@primeuix/themes/types/colorpicker';\n\nexport const root: ColorPickerTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const preview: ColorPickerTokenSections.Preview = {\n    width: '1.75rem',\n    height: '1.75rem',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    }\n};\n\nexport const panel: ColorPickerTokenSections.Panel = {\n    shadow: '{overlay.popover.shadow}',\n    borderRadius: '{overlay.popover.borderRadius}'\n};\n\nexport const colorScheme: ColorPickerTokenSections.ColorScheme = {\n    light: {\n        panel: {\n            background: '{surface.800}',\n            borderColor: '{surface.900}'\n        },\n        handle: {\n            color: '{surface.0}'\n        }\n    },\n    dark: {\n        panel: {\n            background: '{surface.900}',\n            borderColor: '{surface.700}'\n        },\n        handle: {\n            color: '{surface.0}'\n        }\n    }\n};\n\nexport default {\n    root,\n    preview,\n    panel,\n    colorScheme\n} satisfies ColorPickerDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAsC;AAC/C;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;EACP,cAAQ;EACR,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,QAAwC;EACjD,cAAQ;AACR;IACJ,IAAA;EAEa,OAAA;IACT,OAAO;MACH,YAAO;MACH,aAAY;IACZ;IACJ,QAAA;MACA,OAAQ;IACJ;EACJ;EACJ,MAAA;IACA,OAAM;MACF,YAAO;MACH,aAAY;IACZ;IACJ,QAAA;MACA,OAAQ;IACJ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;EACA,OAAA;EACA,aAAA;AACA;", "names": []}