{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/floatlabel/index.ts"], "sourcesContent": ["import type { FloatLabelDesignTokens, FloatLabelTokenSections } from '@primeuix/themes/types/floatlabel';\n\nexport const root: FloatLabelTokenSections.Root = {\n    color: '{form.field.float.label.color}',\n    focusColor: '{form.field.float.label.focus.color}',\n    activeColor: '{form.field.float.label.active.color}',\n    invalidColor: '{form.field.float.label.invalid.color}',\n    transitionDuration: '0.2s',\n    positionX: '{form.field.padding.x}',\n    positionY: '{form.field.padding.y}',\n    fontWeight: '500',\n    active: {\n        fontSize: '0.75rem',\n        fontWeight: '400'\n    }\n};\n\nexport const over: FloatLabelTokenSections.Over = {\n    active: {\n        top: '-1.375rem'\n    }\n};\n\nexport const inside: FloatLabelTokenSections.In = {\n    input: {\n        paddingTop: '1.875rem',\n        paddingBottom: '{form.field.padding.y}'\n    },\n    active: {\n        top: '{form.field.padding.y}'\n    }\n};\n\nexport const on: FloatLabelTokenSections.On = {\n    borderRadius: '{border.radius.xs}',\n    active: {\n        background: '{form.field.background}',\n        padding: '0 0.125rem'\n    }\n};\n\nexport default {\n    root,\n    over,\n    in: inside,\n    on\n} satisfies FloatLabelDesignTokens;\n"], "mappings": ";;;;;EAEa,OAAqC;EAC9C,YAAO;EACP,aAAY;EACZ,cAAa;EACb,oBAAc;EACd,WAAA;EACA,WAAW;EACX,YAAW;EACX,QAAA;IACA,UAAQ;IACJ,YAAU;EACV;AACJ;IACJ,IAAA;EAEa,QAAqC;IAC9C,KAAQ;EACJ;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,YAAO;IACH,eAAY;EACZ;EACJ,QAAA;IACA,KAAQ;EACJ;AACJ;IACJ,IAAA;EAEa,cAAiC;EAC1C,QAAA;IACA,YAAQ;IACJ,SAAA;EACA;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,IAAA;EACA,IAAI;AACJ;", "names": []}