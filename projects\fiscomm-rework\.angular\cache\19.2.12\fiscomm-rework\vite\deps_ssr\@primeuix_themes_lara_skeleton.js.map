{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/skeleton/index.ts"], "sourcesContent": ["import type { SkeletonDesignTokens, SkeletonTokenSections } from '@primeuix/themes/types/skeleton';\n\nexport const root: SkeletonTokenSections.Root = {\n    borderRadius: '{content.border.radius}'\n};\n\nexport const colorScheme: SkeletonTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.200}',\n            animationBackground: 'rgba(255,255,255,0.4)'\n        }\n    },\n    dark: {\n        root: {\n            background: 'rgba(255, 255, 255, 0.06)',\n            animationBackground: 'rgba(255, 255, 255, 0.04)'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies SkeletonDesignTokens;\n"], "mappings": ";;;;;EAEa,cAAmC;AAC5C;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,qBAAY;IACZ;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,qBAAY;IACZ;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,aAAA;AACA;", "names": []}