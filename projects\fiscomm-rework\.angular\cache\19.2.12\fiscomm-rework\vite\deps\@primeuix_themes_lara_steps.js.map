{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/steps/index.ts"], "sourcesContent": ["import type { StepsDesignTokens, StepsTokenSections } from '@primeuix/themes/types/steps';\n\nexport const root: StepsTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const separator: StepsTokenSections.Separator = {\n    background: '{content.border.color}'\n};\n\nexport const itemLink: StepsTokenSections.ItemLink = {\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    },\n    gap: '0.5rem'\n};\n\nexport const itemLabel: StepsTokenSections.ItemLabel = {\n    color: '{text.muted.color}',\n    activeColor: '{primary.color}',\n    fontWeight: '500'\n};\n\nexport const itemNumber: StepsTokenSections.ItemNumber = {\n    background: '{content.background}',\n    activeBackground: '{primary.color}',\n    borderColor: '{content.border.color}',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    activeColor: '{primary.contrast.color}',\n    size: '2.25rem',\n    fontSize: '1.125rem',\n    fontWeight: '500',\n    borderRadius: '50%',\n    shadow: 'none'\n};\n\nexport default {\n    root,\n    separator,\n    itemLink,\n    itemLabel,\n    itemNumber\n} satisfies StepsDesignTokens;\n"], "mappings": ";;;;EAEa,oBAAgC;AACzC;IACJ,IAAA;EAEa,YAA0C;AACnD;IACJ,IAAA;EAEa,cAAwC;EACjD,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,KAAA;AACA;IACJ,IAAA;EAEa,OAAA;EACT,aAAO;EACP,YAAa;AACb;IACJ,IAAA;EAEa,YAAA;EACT,kBAAY;EACZ,aAAA;EACA,mBAAa;EACb,OAAA;EACA,aAAO;EACP,MAAA;EACA,UAAM;EACN,YAAU;EACV,cAAY;EACZ,QAAA;AACA;IACJ,IAAA;EAEO,MAAA;EACH,WAAA;EACA,UAAA;EACA,WAAA;EACA,YAAA;AACA;", "names": []}