import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  BEFORE_APP_SERIALIZED,
  ENABLE_DOM_EMULATION,
  INITIAL_CONFIG,
  INTERNAL_SERVER_PLATFORM_PROVIDERS,
  PlatformState,
  SERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderInternal,
  renderModule
} from "./chunk-7YX4WRMU.js";
import "./chunk-BYQ4IJ2D.js";
import "./chunk-WRNSNRRK.js";
import "./chunk-GLXHJZNN.js";
import "./chunk-FW7PY6T7.js";
import "./chunk-YHCV7DAQ.js";
export {
  BEFORE_APP_SERIALIZED,
  INITIAL_CONFIG,
  PlatformState,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderModule,
  ENABLE_DOM_EMULATION as ɵENABLE_DOM_EMULATION,
  INTERNAL_SERVER_PLATFORM_PROVIDERS as ɵINTERNAL_SERVER_PLATFORM_PROVIDERS,
  SERVER_CONTEXT as ɵSERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS as ɵSERVER_RENDER_PROVIDERS,
  renderInternal as ɵrenderInternal
};
