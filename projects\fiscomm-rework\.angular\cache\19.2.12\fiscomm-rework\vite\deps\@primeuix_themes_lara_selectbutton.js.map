{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/selectbutton/index.ts"], "sourcesContent": ["import type { SelectButtonDesignTokens, SelectButtonTokenSections } from '@primeuix/themes/types/selectbutton';\n\nexport const root: SelectButtonTokenSections.Root = {\n    borderRadius: '{form.field.border.radius}'\n};\n\nexport const colorScheme: SelectButtonTokenSections.ColorScheme = {\n    light: {\n        root: {\n            invalidBorderColor: '{form.field.invalid.border.color}'\n        }\n    },\n    dark: {\n        root: {\n            invalidBorderColor: '{form.field.invalid.border.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies SelectButtonDesignTokens;\n"], "mappings": ";;;;EAEa,cAAuC;AAChD;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,oBAAM;IACF;EACJ;EACJ,MAAA;IACA,MAAM;MACF,oBAAM;IACF;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,aAAA;AACA;", "names": []}