import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/tree/index.mjs
var o = {
  background: "{content.background}",
  color: "{content.color}",
  padding: "1rem",
  gap: "2px",
  indent: "1rem",
  transitionDuration: "{transition.duration}"
};
var r = {
  padding: "0.375rem 0.625rem",
  borderRadius: "{content.border.radius}",
  hoverBackground: "{content.hover.background}",
  selectedBackground: "{highlight.background}",
  color: "{text.color}",
  hoverColor: "{text.hover.color}",
  selectedColor: "{highlight.color}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "inset {focus.ring.shadow}"
  },
  gap: "0.25rem"
};
var e = {
  color: "{text.muted.color}",
  hoverColor: "{text.hover.muted.color}",
  selectedColor: "{highlight.color}"
};
var t = {
  borderRadius: "50%",
  size: "1.75rem",
  hoverBackground: "{content.hover.background}",
  selectedHoverBackground: "{content.background}",
  color: "{text.muted.color}",
  hoverColor: "{text.hover.muted.color}",
  selectedHoverColor: "{primary.color}",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var c = {
  size: "2rem"
};
var n = {
  margin: "0 0 0.5rem 0"
};
var d = {
  root: o,
  node: r,
  nodeIcon: e,
  nodeToggleButton: t,
  loadingIcon: c,
  filter: n
};
export {
  d as default,
  n as filter,
  c as loadingIcon,
  r as node,
  e as nodeIcon,
  t as nodeToggleButton,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_tree.js.map
