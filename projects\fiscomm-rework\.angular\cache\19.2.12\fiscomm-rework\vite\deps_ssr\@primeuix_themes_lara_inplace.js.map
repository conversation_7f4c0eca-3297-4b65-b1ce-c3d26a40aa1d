{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/inplace/index.ts"], "sourcesContent": ["import type { InplaceDesignTokens, InplaceTokenSections } from '@primeuix/themes/types/inplace';\n\nexport const root: InplaceTokenSections.Root = {\n    padding: '{form.field.padding.y} {form.field.padding.x}',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    },\n    transitionDuration: '{transition.duration}'\n};\n\nexport const display: InplaceTokenSections.Display = {\n    hoverBackground: '{content.hover.background}',\n    hoverColor: '{content.hover.color}'\n};\n\nexport default {\n    root,\n    display\n} satisfies InplaceDesignTokens;\n"], "mappings": ";;;;;EAEa,SAAkC;EAC3C,cAAS;EACT,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;AACA;IACJ,IAAA;EAEa,iBAAwC;EACjD,YAAA;AACA;IACJ,IAAA;EAEO,MAAA;EACH,SAAA;AACA;", "names": []}