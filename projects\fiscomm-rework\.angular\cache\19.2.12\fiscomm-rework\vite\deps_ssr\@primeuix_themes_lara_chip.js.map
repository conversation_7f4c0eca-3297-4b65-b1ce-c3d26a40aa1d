{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/chip/index.ts"], "sourcesContent": ["import type { ChipDesignTokens, ChipTokenSections } from '@primeuix/themes/types/chip';\n\nexport const root: ChipTokenSections.Root = {\n    borderRadius: '16px',\n    paddingX: '0.875rem',\n    paddingY: '0.625rem',\n    gap: '0.5rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const image: ChipTokenSections.Image = {\n    width: '2rem',\n    height: '2rem'\n};\n\nexport const icon: ChipTokenSections.Icon = {\n    size: '1rem'\n};\n\nexport const removeIcon: ChipTokenSections.RemoveIcon = {\n    size: '1rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: ChipTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.100}',\n            color: '{surface.800}'\n        },\n        icon: {\n            color: '{surface.800}'\n        },\n        removeIcon: {\n            color: '{surface.800}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.800}',\n            color: '{surface.0}'\n        },\n        icon: {\n            color: '{surface.0}'\n        },\n        removeIcon: {\n            color: '{surface.0}'\n        }\n    }\n};\n\nexport default {\n    root,\n    image,\n    icon,\n    removeIcon,\n    colorScheme\n} satisfies ChipDesignTokens;\n"], "mappings": ";;;;;EAEa,cAA+B;EACxC,UAAA;EACA,UAAU;EACV,KAAA;EACA,oBAAK;AACL;IACJ,IAAA;EAEa,OAAA;EACT,QAAO;AACP;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,MAAA;EACT,WAAM;IACN,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,OAAM;IACF;IACJ,YAAA;MACA,OAAA;IACI;EACJ;EACJ,MAAA;IACA,MAAM;MACF,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,OAAM;IACF;IACJ,YAAA;MACA,OAAA;IACI;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,MAAA;EACA,YAAA;EACA,aAAA;AACA;", "names": []}