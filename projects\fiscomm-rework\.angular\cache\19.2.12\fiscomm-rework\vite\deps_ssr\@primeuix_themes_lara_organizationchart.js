import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/organizationchart/index.mjs
var o = {
  gutter: "0.75rem",
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "{content.background}",
  hoverBackground: "{content.hover.background}",
  selectedBackground: "{highlight.background}",
  borderColor: "{content.border.color}",
  color: "{content.color}",
  selectedColor: "{highlight.color}",
  hoverColor: "{content.hover.color}",
  padding: "1rem 1.25rem",
  toggleablePadding: "1rem 1.25rem 1.5rem 1.25rem",
  borderRadius: "{content.border.radius}"
};
var e = {
  background: "{content.background}",
  hoverBackground: "{content.hover.background}",
  borderColor: "{content.border.color}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  size: "1.75rem",
  borderRadius: "50%",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var t = {
  color: "{content.border.color}",
  borderRadius: "{content.border.radius}",
  height: "24px"
};
var n = {
  root: o,
  node: r,
  nodeToggleButton: e,
  connector: t
};
export {
  t as connector,
  n as default,
  r as node,
  e as nodeToggleButton,
  o as root
};
//# sourceMappingURL=@primeuix_themes_lara_organizationchart.js.map
