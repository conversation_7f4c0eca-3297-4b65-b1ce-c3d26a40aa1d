{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/breadcrumb/index.ts"], "sourcesContent": ["import type { BreadcrumbDesignTokens, BreadcrumbTokenSections } from '@primeuix/themes/types/breadcrumb';\n\nexport const root: BreadcrumbTokenSections.Root = {\n    padding: '1.25rem',\n    background: '{content.background}',\n    gap: '0.5rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const item: BreadcrumbTokenSections.Item = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    borderRadius: '{content.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        hoverColor: '{navigation.item.icon.focus.color}'\n    },\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const separator: BreadcrumbTokenSections.Separator = {\n    color: '{navigation.item.icon.color}'\n};\n\nexport default {\n    root,\n    item,\n    separator\n} satisfies BreadcrumbDesignTokens;\n"], "mappings": ";;;;;EAEa,SAAqC;EAC9C,YAAS;EACT,KAAA;EACA,oBAAK;AACL;IACJ,IAAA;EAEa,OAAqC;EAC9C,YAAO;EACP,cAAY;EACZ,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;EACP;EACJ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,WAAA;AACA;", "names": []}