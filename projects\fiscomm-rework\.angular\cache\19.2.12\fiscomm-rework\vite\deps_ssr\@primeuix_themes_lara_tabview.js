import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/tabview/index.mjs
var o = {
  transitionDuration: "{transition.duration}"
};
var r = {
  background: "{content.background}",
  borderColor: "{content.border.color}"
};
var t = {
  borderColor: "{content.border.color}",
  activeBorderColor: "{primary.color}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  activeColor: "{primary.color}"
};
var n = {
  background: "{content.background}",
  color: "{content.color}"
};
var a = {
  background: "{content.background}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}"
};
var c = {
  light: {
    navButton: {
      shadow: "0px 0px 10px 50px rgba(255, 255, 255, 0.6)"
    }
  },
  dark: {
    navButton: {
      shadow: "0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"
    }
  }
};
var e = {
  root: o,
  tabList: r,
  tab: t,
  tabPanel: n,
  navButton: a,
  colorScheme: c
};
export {
  c as colorScheme,
  e as default,
  a as navButton,
  o as root,
  t as tab,
  r as tabList,
  n as tabPanel
};
//# sourceMappingURL=@primeuix_themes_lara_tabview.js.map
