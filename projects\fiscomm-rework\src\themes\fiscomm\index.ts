import type { Preset } from '@primeuix/themes/types';
import type { LaraBaseDesignTokens } from './base/index.d';

import accordion from '../fiscomm/accordion';
import autocomplete from '../fiscomm//autocomplete';
import avatar from '../fiscomm//avatar';
import badge from '../fiscomm//badge';
import base from '../fiscomm//base';
import blockui from '../fiscomm//blockui';
import breadcrumb from '../fiscomm//breadcrumb';
import button from '../fiscomm//button';
import card from '../fiscomm//card';
import carousel from '../fiscomm//carousel';
import cascadeselect from '../fiscomm//cascadeselect';
import checkbox from '../fiscomm//checkbox';
import chip from '../fiscomm//chip';
import colorpicker from '../fiscomm//colorpicker';
import confirmdialog from '../fiscomm//confirmdialog';
import confirmpopup from '../fiscomm//confirmpopup';
import contextmenu from '../fiscomm//contextmenu';
import datatable from '../fiscomm//datatable';
import dataview from '../fiscomm//dataview';
import datepicker from '../fiscomm//datepicker';
import dialog from '../fiscomm//dialog';
import divider from '../fiscomm//divider';
import dock from '../fiscomm//dock';
import drawer from '../fiscomm//drawer';
import editor from '../fiscomm//editor';
import fieldset from '../fiscomm//fieldset';
import fileupload from '../fiscomm//fileupload';
import floatlabel from '../fiscomm//floatlabel';
import galleria from '../fiscomm//galleria';
import iconfield from '../fiscomm//iconfield';
import iftalabel from '../fiscomm//iftalabel';
import image from '../fiscomm//image';
import imagecompare from '../fiscomm//imagecompare';
import inlinemessage from '../fiscomm//inlinemessage';
import inplace from '../fiscomm//inplace';
import inputchips from '../fiscomm//inputchips';
import inputgroup from '../fiscomm//inputgroup';
import inputnumber from '../fiscomm//inputnumber';
import inputotp from '../fiscomm//inputotp';
import inputtext from '../fiscomm//inputtext';
import knob from '../fiscomm//knob';
import listbox from '../fiscomm//listbox';
import megamenu from '../fiscomm//megamenu';
import menu from '../fiscomm//menu';
import menubar from '../fiscomm//menubar';
import message from '../fiscomm//message';
import metergroup from '../fiscomm//metergroup';
import multiselect from '../fiscomm//multiselect';
import orderlist from '../fiscomm//orderlist';
import organizationchart from '../fiscomm//organizationchart';
import overlaybadge from '../fiscomm//overlaybadge';
import paginator from '../fiscomm//paginator';
import panel from '../fiscomm//panel';
import panelmenu from '../fiscomm//panelmenu';
import password from '../fiscomm//password';
import picklist from '../fiscomm//picklist';
import popover from '../fiscomm//popover';
import progressbar from '../fiscomm//progressbar';
import progressspinner from '../fiscomm//progressspinner';
import radiobutton from '../fiscomm//radiobutton';
import rating from '../fiscomm//rating';
import ripple from '../fiscomm//ripple';
import scrollpanel from '../fiscomm//scrollpanel';
import select from '../fiscomm//select';
import selectbutton from '../fiscomm//selectbutton';
import skeleton from '../fiscomm//skeleton';
import slider from '../fiscomm//slider';
import speeddial from '../fiscomm//speeddial';
import splitbutton from '../fiscomm//splitbutton';
import splitter from '../fiscomm//splitter';
import stepper from '../fiscomm//stepper';
import steps from '../fiscomm//steps';
import tabmenu from '../fiscomm//tabmenu';
import tabs from '../fiscomm//tabs';
import tabview from '../fiscomm//tabview';
import tag from '../fiscomm//tag';
import terminal from '../fiscomm//terminal';
import textarea from '../fiscomm//textarea';
import tieredmenu from '../fiscomm//tieredmenu';
import timeline from '../fiscomm//timeline';
import toast from '../fiscomm//toast';
import togglebutton from '../fiscomm//togglebutton';
import toggleswitch from '../fiscomm//toggleswitch';
import toolbar from '../fiscomm//toolbar';
import tooltip from '../fiscomm//tooltip';
import tree from '../fiscomm//tree';
import treeselect from '../fiscomm//treeselect';
import treetable from '../fiscomm//treetable';
import virtualscroller from '../fiscomm//virtualscroller';

export default {
  ...base,
  components: {
    accordion,
    autocomplete,
    avatar,
    badge,
    blockui,
    breadcrumb,
    button,
    datepicker,
    card,
    carousel,
    cascadeselect,
    checkbox,
    chip,
    colorpicker,
    confirmdialog,
    confirmpopup,
    contextmenu,
    dataview,
    datatable,
    dialog,
    divider,
    dock,
    drawer,
    editor,
    fieldset,
    fileupload,
    iftalabel,
    floatlabel,
    galleria,
    iconfield,
    image,
    imagecompare,
    inlinemessage,
    inplace,
    inputchips,
    inputgroup,
    inputnumber,
    inputotp,
    inputtext,
    knob,
    listbox,
    megamenu,
    menu,
    menubar,
    message,
    metergroup,
    multiselect,
    orderlist,
    organizationchart,
    overlaybadge,
    popover,
    paginator,
    password,
    panel,
    panelmenu,
    picklist,
    progressbar,
    progressspinner,
    radiobutton,
    rating,
    ripple,
    scrollpanel,
    select,
    selectbutton,
    skeleton,
    slider,
    speeddial,
    splitter,
    splitbutton,
    stepper,
    steps,
    tabmenu,
    tabs,
    tabview,
    textarea,
    tieredmenu,
    tag,
    terminal,
    timeline,
    togglebutton,
    toggleswitch,
    tree,
    treeselect,
    treetable,
    toast,
    toolbar,
    tooltip,
    virtualscroller,
  },
} satisfies Preset<LaraBaseDesignTokens>;
