{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/badge/index.ts"], "sourcesContent": ["import type { BadgeDesignTokens, BadgeTokenSections } from '@primeuix/themes/types/badge';\n\nexport const root: BadgeTokenSections.Root = {\n    borderRadius: '{border.radius.md}',\n    padding: '0 0.5rem',\n    fontSize: '0.75rem',\n    fontWeight: '700',\n    minWidth: '1.5rem',\n    height: '1.5rem'\n};\nexport const dot: BadgeTokenSections.Dot = {\n    size: '0.5rem'\n};\nexport const sm: BadgeTokenSections.Sm = {\n    fontSize: '0.625rem',\n    minWidth: '1.25rem',\n    height: '1.25rem'\n};\n\nexport const lg: BadgeTokenSections.Lg = {\n    fontSize: '0.875rem',\n    minWidth: '1.75rem',\n    height: '1.75rem'\n};\n\nexport const xl: BadgeTokenSections.Xl = {\n    fontSize: '1rem',\n    minWidth: '2rem',\n    height: '2rem'\n};\n\nexport const colorScheme: BadgeTokenSections.ColorScheme = {\n    light: {\n        primary: {\n            background: '{primary.color}',\n            color: '{primary.contrast.color}'\n        },\n        secondary: {\n            background: '{surface.100}',\n            color: '{surface.600}'\n        },\n        success: {\n            background: '{green.500}',\n            color: '{surface.0}'\n        },\n        info: {\n            background: '{sky.500}',\n            color: '{surface.0}'\n        },\n        warn: {\n            background: '{orange.500}',\n            color: '{surface.0}'\n        },\n        danger: {\n            background: '{red.500}',\n            color: '{surface.0}'\n        },\n        contrast: {\n            background: '{surface.950}',\n            color: '{surface.0}'\n        }\n    },\n    dark: {\n        primary: {\n            background: '{primary.color}',\n            color: '{primary.contrast.color}'\n        },\n        secondary: {\n            background: '{surface.800}',\n            color: '{surface.300}'\n        },\n        success: {\n            background: '{green.400}',\n            color: '{green.950}'\n        },\n        info: {\n            background: '{sky.400}',\n            color: '{sky.950}'\n        },\n        warn: {\n            background: '{orange.400}',\n            color: '{orange.950}'\n        },\n        danger: {\n            background: '{red.400}',\n            color: '{red.950}'\n        },\n        contrast: {\n            background: '{surface.0}',\n            color: '{surface.950}'\n        }\n    }\n};\n\nexport default {\n    root,\n    dot,\n    sm,\n    lg,\n    xl,\n    colorScheme\n} satisfies BadgeDesignTokens;\n"], "mappings": ";;;;EAEa,cAAgC;EACzC,SAAA;EACA,UAAS;EACT,YAAU;EACV,UAAY;EACZ,QAAU;AACV;IACJ,IAAA;EACa,MAA8B;AACvC;IACJ,IAAA;EACa,UAA4B;EACrC,UAAU;EACV,QAAU;AACV;IACJ,IAAA;EAEa,UAA4B;EACrC,UAAU;EACV,QAAU;AACV;IACJ,IAAA;EAEa,UAA4B;EACrC,UAAU;EACV,QAAU;AACV;IACJ,IAAA;EAEa,OAAA;IACT,SAAO;MACH,YAAS;MACL,OAAA;IACA;IACJ,WAAA;MACA,YAAW;MACP,OAAA;IACA;IACJ,SAAA;MACA,YAAS;MACL,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,OAAA;IACA;EACJ;EACJ,MAAA;IACA,SAAM;MACF,YAAS;MACL,OAAA;IACA;IACJ,WAAA;MACA,YAAW;MACP,OAAA;IACA;IACJ,SAAA;MACA,YAAS;MACL,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,MAAA;MACA,YAAM;MACF,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,UAAA;MACA,YAAU;MACN,OAAA;IACA;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,KAAA;EACA,IAAA;EACA,IAAA;EACA,IAAA;EACA,aAAA;AACA;", "names": []}