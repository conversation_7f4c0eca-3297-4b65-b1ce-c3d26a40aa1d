{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/tieredmenu/index.ts"], "sourcesContent": ["import type { TieredMenuDesignTokens, TieredMenuTokenSections } from '@primeuix/themes/types/tieredmenu';\n\nexport const root: TieredMenuTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const list: TieredMenuTokenSections.List = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const item: TieredMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const submenu: TieredMenuTokenSections.Submenu = {\n    mobileIndent: '1.25rem'\n};\n\nexport const submenuIcon: TieredMenuTokenSections.SubmenuIcon = {\n    size: '{navigation.submenu.icon.size}',\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}',\n    activeColor: '{navigation.submenu.icon.active.color}'\n};\n\nexport const separator: TieredMenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport default {\n    root,\n    list,\n    item,\n    submenu,\n    submenuIcon,\n    separator\n} satisfies TieredMenuDesignTokens;\n"], "mappings": ";;;;;EAEa,YAAqC;EAC9C,aAAY;EACZ,OAAA;EACA,cAAO;EACP,QAAA;EACA,oBAAQ;AACR;IACJ,IAAA;EAEa,SAAqC;EAC9C,KAAA;AACA;IACJ,IAAA;EAEa,iBAAqC;EAC9C,kBAAiB;EACjB,OAAA;EACA,YAAO;EACP,aAAY;EACZ,SAAA;EACA,cAAS;EACT,KAAA;EACA,MAAK;IACL,OAAM;IACF,YAAO;IACP,aAAY;EACZ;AACJ;IACJ,IAAA;EAEa,cAA2C;AACpD;IACJ,IAAA;EAEa,MAAA;EACT,OAAM;EACN,YAAO;EACP,aAAY;AACZ;IACJ,IAAA;EAEa,aAA+C;AACxD;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,MAAA;EACA,SAAA;EACA,aAAA;EACA,WAAA;AACA;", "names": []}