import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-YHCV7DAQ.js";

// node_modules/@primeuix/themes/lara/tabmenu/index.mjs
var o = {
  transitionDuration: "{transition.duration}"
};
var r = {
  borderWidth: "0",
  background: "{content.background}",
  borderColor: "{content.border.color}"
};
var t = {
  borderWidth: "2px 0 0 0",
  borderColor: "transparent",
  hoverBorderColor: "transparent",
  activeBorderColor: "{primary.color}",
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  activeColor: "{primary.color}",
  padding: "1rem 1.25rem",
  fontWeight: "600",
  margin: "0",
  gap: "0.5rem",
  focusRing: {
    width: "{focus.ring.width}",
    style: "{focus.ring.style}",
    color: "{focus.ring.color}",
    offset: "{focus.ring.offset}",
    shadow: "{focus.ring.shadow}"
  }
};
var e = {
  color: "{text.muted.color}",
  hoverColor: "{text.color}",
  activeColor: "{primary.color}"
};
var a = {
  height: "0",
  bottom: "0",
  background: "transparent"
};
var c = {
  light: {
    item: {
      background: "{surface.50}",
      hoverBackground: "{surface.100}",
      activeBackground: "{surface.0}"
    }
  },
  dark: {
    item: {
      background: "{surface.800}",
      hoverBackground: "{surface.700}",
      activeBackground: "{surface.900}"
    }
  }
};
var i = {
  root: o,
  tablist: r,
  item: t,
  itemIcon: e,
  activeBar: a,
  colorScheme: c
};
export {
  a as activeBar,
  c as colorScheme,
  i as default,
  t as item,
  e as itemIcon,
  o as root,
  r as tablist
};
//# sourceMappingURL=@primeuix_themes_lara_tabmenu.js.map
