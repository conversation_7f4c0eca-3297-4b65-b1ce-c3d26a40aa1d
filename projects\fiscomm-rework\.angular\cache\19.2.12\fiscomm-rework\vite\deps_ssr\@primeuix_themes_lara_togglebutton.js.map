{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/togglebutton/index.ts"], "sourcesContent": ["import type { ToggleButtonDesignTokens, ToggleButtonTokenSections } from '@primeuix/themes/types/togglebutton';\n\nexport const root: ToggleButtonTokenSections.Root = {\n    padding: '0.625rem 1rem',\n    borderRadius: '{content.border.radius}',\n    gap: '0.5rem',\n    fontWeight: '500',\n    background: '{form.field.background}',\n    borderColor: '{form.field.border.color}',\n    color: '{form.field.color}',\n    hoverColor: '{form.field.color}',\n    checkedBackground: '{highlight.background}',\n    checkedColor: '{highlight.color}',\n    checkedBorderColor: '{form.field.border.color}',\n    disabledBackground: '{form.field.disabled.background}',\n    disabledBorderColor: '{form.field.disabled.background}',\n    disabledColor: '{form.field.disabled.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        fontSize: '{form.field.sm.font.size}',\n        padding: '0.5rem 0.75rem'\n    },\n    lg: {\n        fontSize: '{form.field.lg.font.size}',\n        padding: '0.75rem 1.25rem'\n    }\n};\n\nexport const icon: ToggleButtonTokenSections.Icon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.muted.color}',\n    checkedColor: '{highlight.color}',\n    disabledColor: '{form.field.disabled.color}'\n};\n\nexport const content: ToggleButtonTokenSections.Content = {\n    checkedBackground: 'transparent',\n    checkedShadow: 'none',\n    padding: '0',\n    borderRadius: '0',\n    sm: {\n        padding: '0'\n    },\n    lg: {\n        padding: '0'\n    }\n};\n\nexport const colorScheme: ToggleButtonTokenSections.ColorScheme = {\n    light: {\n        root: {\n            hoverBackground: '{surface.100}'\n        }\n    },\n    dark: {\n        root: {\n            hoverBackground: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    root,\n    icon,\n    content,\n    colorScheme\n} satisfies ToggleButtonDesignTokens;\n"], "mappings": ";;;;;EAEa,SAAuC;EAChD,cAAS;EACT,KAAA;EACA,YAAK;EACL,YAAY;EACZ,aAAY;EACZ,OAAA;EACA,YAAO;EACP,mBAAY;EACZ,cAAA;EACA,oBAAc;EACd,oBAAoB;EACpB,qBAAoB;EACpB,eAAA;EACA,oBAAe;EACf,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,oBAAA;EACA,IAAA;IACI,UAAA;IACA,SAAU;EACV;EACJ,IAAA;IACI,UAAA;IACA,SAAU;EACV;AACJ;IACJ,IAAA;EAEa,OAAuC;EAChD,YAAO;EACP,cAAY;EACZ,eAAc;AACd;IACJ,IAAA;EAEa,mBAA6C;EACtD,eAAA;EACA,SAAA;EACA,cAAS;EACT,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,iBAAM;IACF;EACJ;EACJ,MAAA;IACA,MAAM;MACF,iBAAM;IACF;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,MAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}