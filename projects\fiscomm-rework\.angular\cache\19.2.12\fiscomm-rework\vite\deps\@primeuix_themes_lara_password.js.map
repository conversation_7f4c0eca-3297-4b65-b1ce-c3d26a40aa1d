{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/password/index.ts"], "sourcesContent": ["import type { PasswordDesignTokens, PasswordTokenSections } from '@primeuix/themes/types/password';\n\nexport const meter: PasswordTokenSections.Meter = {\n    background: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    height: '.75rem'\n};\n\nexport const icon: PasswordTokenSections.Icon = {\n    color: '{form.field.icon.color}'\n};\n\nexport const overlay: PasswordTokenSections.Overlay = {\n    background: '{overlay.popover.background}',\n    borderColor: '{overlay.popover.border.color}',\n    borderRadius: '{overlay.popover.border.radius}',\n    color: '{overlay.popover.color}',\n    padding: '{overlay.popover.padding}',\n    shadow: '{overlay.popover.shadow}'\n};\n\nexport const content: PasswordTokenSections.Content = {\n    gap: '0.75rem'\n};\n\nexport const colorScheme: PasswordTokenSections.ColorScheme = {\n    light: {\n        strength: {\n            weakBackground: '{red.500}',\n            mediumBackground: '{amber.500}',\n            strongBackground: '{green.500}'\n        }\n    },\n    dark: {\n        strength: {\n            weakBackground: '{red.400}',\n            mediumBackground: '{amber.400}',\n            strongBackground: '{green.400}'\n        }\n    }\n};\n\nexport default {\n    meter,\n    icon,\n    overlay,\n    content,\n    colorScheme\n} satisfies PasswordDesignTokens;\n"], "mappings": ";;;;EAEa,YAAqC;EAC9C,cAAY;EACZ,QAAA;AACA;IACJ,IAAA;EAEa,OAAmC;AAC5C;IACJ,IAAA;EAEa,YAAyC;EAClD,aAAY;EACZ,cAAa;EACb,OAAA;EACA,SAAO;EACP,QAAS;AACT;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,OAAA;IACT,UAAO;MACH,gBAAU;MACN,kBAAgB;MAChB,kBAAkB;IAClB;EACJ;EACJ,MAAA;IACA,UAAM;MACF,gBAAU;MACN,kBAAgB;MAChB,kBAAkB;IAClB;EACJ;AACJ;IACJ,IAAA;EAEO,OAAA;EACH,MAAA;EACA,SAAA;EACA,SAAA;EACA,aAAA;AACA;", "names": []}