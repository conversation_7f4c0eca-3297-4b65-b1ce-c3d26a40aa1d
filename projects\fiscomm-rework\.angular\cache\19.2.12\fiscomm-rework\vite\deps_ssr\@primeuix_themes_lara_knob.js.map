{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/knob/index.ts"], "sourcesContent": ["import type { KnobDesignTokens, KnobTokenSections } from '@primeuix/themes/types/knob';\n\nexport const root: KnobTokenSections.Root = {\n    transitionDuration: '{transition.duration}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const value: KnobTokenSections.Value = {\n    background: '{primary.color}'\n};\n\nexport const range: KnobTokenSections.Range = {\n    background: '{content.border.color}'\n};\n\nexport const text: KnobTokenSections.Text = {\n    color: '{text.muted.color}'\n};\n\nexport default {\n    root,\n    value,\n    range,\n    text\n} satisfies KnobDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAA+B;EACxC,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,YAAiC;AAC1C;IACJ,IAAA;EAEa,YAAiC;AAC1C;IACJ,IAAA;EAEa,OAA+B;AACxC;IACJ,IAAA;EAEO,MAAA;EACH,OAAA;EACA,OAAA;EACA,MAAA;AACA;", "names": []}