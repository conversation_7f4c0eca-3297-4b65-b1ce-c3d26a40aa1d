{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/picklist/index.ts"], "sourcesContent": ["import type { PickListDesignTokens, PickListTokenSections } from '@primeuix/themes/types/picklist';\n\nexport const root: PickListTokenSections.Root = {\n    gap: '1.125rem'\n};\n\nexport const controls: PickListTokenSections.Controls = {\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    controls\n} satisfies PickListDesignTokens;\n"], "mappings": ";;;;;EAEa,KAAA;AACT;IACJ,IAAA;EAEa,KAAA;AACT;IACJ,IAAA;EAEO,MAAA;EACH,UAAA;AACA;", "names": []}