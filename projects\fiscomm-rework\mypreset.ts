import { definePreset } from '@primeng/themes';
//import <PERSON> from '@primeng/themes/aura';
import <PERSON> from './src/themes/fiscomm';

const MyPreset = definePreset(<PERSON>, {
  semantic: {
    primary: {
      50: '#C7F2ED',
      100: '#A2E0DA',
      200: '#7DCFC6',
      300: '#58BDB3',
      400: '#37A2A0',
      500: '#15878D',
      600: '#0B6676',
      700: '#00455F',
      surface: '#58BDB330',
    },
    secondary: {
      50: '#F1F3F9',
      100: '#E1E7F0',
      200: '#CDD4E1',
      300: '#B8C0D2',
      400: '#A3ACC3',
      500: '#8E98B4',
      600: '#7A85A5',
      700: '#657196',
      surface: '#B8C0D230',
    },
    info: {
      50: '#E0F2F1',
      100: '#C1E6E3',
      200: '#A2DAD7',
      300: '#83CFD1',
      400: '#64C3CC',
      500: '#45B7C5',
      600: '#3794A7',
      700: '#297189',
      surface: '#45B7C530',
    },
  },
});

export default MyPreset;
