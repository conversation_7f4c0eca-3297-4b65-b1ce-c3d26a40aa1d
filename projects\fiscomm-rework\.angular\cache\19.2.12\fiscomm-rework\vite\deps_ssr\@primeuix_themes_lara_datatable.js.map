{"version": 3, "sources": ["../../../../../../node_modules/@primeuix/src/presets/lara/datatable/index.ts"], "sourcesContent": ["import type { DataTableDesignTokens, DataTableTokenSections } from '@primeuix/themes/types/datatable';\n\nexport const root: DataTableTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const header: DataTableTokenSections.Header = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '1px 0 1px 0',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const headerCell: DataTableTokenSections.HeaderCell = {\n    selectedBackground: '{highlight.background}',\n    borderColor: '{datatable.border.color}',\n    hoverColor: '{content.hover.color}',\n    selectedColor: '{highlight.color}',\n    gap: '0.5rem',\n    padding: '0.75rem 1rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    },\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const columnTitle: DataTableTokenSections.ColumnTitle = {\n    fontWeight: '700'\n};\n\nexport const row: DataTableTokenSections.Row = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    color: '{content.color}',\n    hoverColor: '{content.hover.color}',\n    selectedColor: '{highlight.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const bodyCell: DataTableTokenSections.BodyCell = {\n    borderColor: '{datatable.border.color}',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const footerCell: DataTableTokenSections.FooterCell = {\n    borderColor: '{datatable.border.color}',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const columnFooter: DataTableTokenSections.ColumnFooter = {\n    fontWeight: '700'\n};\n\nexport const footer: DataTableTokenSections.Footer = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const dropPoint: DataTableTokenSections.DropPoint = {\n    color: '{primary.color}'\n};\n\nexport const columnResizer: DataTableTokenSections.ColumnResizer = {\n    width: '0.5rem'\n};\n\nexport const resizeIndicator: DataTableTokenSections.ResizeIndicator = {\n    width: '1px',\n    color: '{primary.color}'\n};\n\nexport const sortIcon: DataTableTokenSections.SortIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    size: '0.875rem'\n};\n\nexport const loadingIcon: DataTableTokenSections.LoadingIcon = {\n    size: '2rem'\n};\n\nexport const rowToggleButton: DataTableTokenSections.RowToggleButton = {\n    hoverBackground: '{content.hover.background}',\n    selectedHoverBackground: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    selectedHoverColor: '{primary.color}',\n    size: '1.75rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const filter: DataTableTokenSections.Filter = {\n    inlineGap: '0.5rem',\n    overlaySelect: {\n        background: '{overlay.select.background}',\n        borderColor: '{overlay.select.border.color}',\n        borderRadius: '{overlay.select.border.radius}',\n        color: '{overlay.select.color}',\n        shadow: '{overlay.select.shadow}'\n    },\n    overlayPopover: {\n        background: '{overlay.popover.background}',\n        borderColor: '{overlay.popover.border.color}',\n        borderRadius: '{overlay.popover.border.radius}',\n        color: '{overlay.popover.color}',\n        shadow: '{overlay.popover.shadow}',\n        padding: '{overlay.popover.padding}',\n        gap: '0.5rem'\n    },\n    rule: {\n        borderColor: '{content.border.color}'\n    },\n    constraintList: {\n        padding: '{list.padding}',\n        gap: '{list.gap}'\n    },\n    constraint: {\n        focusBackground: '{list.option.focus.background}',\n        selectedBackground: '{list.option.selected.background}',\n        selectedFocusBackground: '{list.option.selected.focus.background}',\n        color: '{list.option.color}',\n        focusColor: '{list.option.focus.color}',\n        selectedColor: '{list.option.selected.color}',\n        selectedFocusColor: '{list.option.selected.focus.color}',\n        separator: {\n            borderColor: '{content.border.color}'\n        },\n        padding: '{list.option.padding}',\n        borderRadius: '{list.option.border.radius}'\n    }\n};\n\nexport const paginatorTop: DataTableTokenSections.PaginatorTop = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const paginatorBottom: DataTableTokenSections.PaginatorBottom = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const colorScheme: DataTableTokenSections.ColorScheme = {\n    light: {\n        root: {\n            borderColor: '{content.border.color}'\n        },\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        row: {\n            stripedBackground: '{surface.50}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.100}'\n        }\n    },\n    dark: {\n        root: {\n            borderColor: '{surface.800}'\n        },\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        row: {\n            stripedBackground: '{surface.950}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    headerCell,\n    columnTitle,\n    row,\n    bodyCell,\n    footerCell,\n    columnFooter,\n    footer,\n    dropPoint,\n    columnResizer,\n    resizeIndicator,\n    sortIcon,\n    loadingIcon,\n    rowToggleButton,\n    filter,\n    paginatorTop,\n    paginatorBottom,\n    colorScheme\n} satisfies DataTableDesignTokens;\n"], "mappings": ";;;;;EAEa,oBAAoC;AAC7C;IACJ,IAAA;EAEa,aAAwC;EACjD,aAAa;EACb,SAAA;EACA,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,oBAAgD;EACzD,aAAA;EACA,YAAa;EACb,eAAY;EACZ,KAAA;EACA,SAAK;EACL,WAAS;IACT,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;EACJ,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,YAAA;AACT;IACJ,IAAA;EAEa,YAAkC;EAC3C,iBAAY;EACZ,oBAAiB;EACjB,OAAA;EACA,YAAO;EACP,eAAY;EACZ,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,aAA4C;EACrD,SAAA;EACA,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,aAAgD;EACzD,SAAA;EACA,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,YAAA;AACT;IACJ,IAAA;EAEa,aAAwC;EACjD,aAAa;EACb,SAAA;EACA,IAAA;IACI,SAAA;EACA;EACJ,IAAA;IACI,SAAA;EACA;AACJ;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEa,OAAA;AACT;IACJ,IAAA;EAEa,OAAA;EACT,OAAO;AACP;IACJ,IAAA;EAEa,OAAA;EACT,YAAO;EACP,MAAA;AACA;IACJ,IAAA;EAEa,MAAA;AACT;IACJ,IAAA;EAEa,iBAAA;EACT,yBAAiB;EACjB,OAAA;EACA,YAAO;EACP,oBAAY;EACZ,MAAA;EACA,cAAM;EACN,WAAA;IACA,OAAW;IACP,OAAO;IACP,OAAO;IACP,QAAO;IACP,QAAQ;EACR;AACJ;IACJ,IAAA;EAEa,WAAwC;EACjD,eAAW;IACX,YAAe;IACX,aAAY;IACZ,cAAa;IACb,OAAA;IACA,QAAO;EACP;EACJ,gBAAA;IACA,YAAgB;IACZ,aAAY;IACZ,cAAa;IACb,OAAA;IACA,QAAO;IACP,SAAQ;IACR,KAAA;EACA;EACJ,MAAA;IACA,aAAM;EACF;EACJ,gBAAA;IACA,SAAA;IACI,KAAA;EACA;EACJ,YAAA;IACA,iBAAY;IACR,oBAAiB;IACjB,yBAAoB;IACpB,OAAA;IACA,YAAO;IACP,eAAY;IACZ,oBAAe;IACf,WAAA;MACA,aAAW;IACP;IACJ,SAAA;IACA,cAAS;EACT;AACJ;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,aAAA;EACT,aAAa;AACb;IACJ,IAAA;EAEa,OAAA;IACT,MAAO;MACH,aAAM;IACF;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,iBAAY;MACZ,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,OAAA;IACA;IACJ,KAAA;MACA,mBAAK;IACD;IACJ,UAAA;MACA,qBAAU;IACN;EACJ;EACJ,MAAA;IACA,MAAM;MACF,aAAM;IACF;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,iBAAY;MACZ,OAAA;IACA;IACJ,QAAA;MACA,YAAQ;MACJ,OAAA;IACA;IACJ,YAAA;MACA,YAAY;MACR,OAAA;IACA;IACJ,KAAA;MACA,mBAAK;IACD;IACJ,UAAA;MACA,qBAAU;IACN;EACJ;AACJ;IACJ,IAAA;EAEO,MAAA;EACH,QAAA;EACA,YAAA;EACA,aAAA;EACA,KAAA;EACA,UAAA;EACA,YAAA;EACA,cAAA;EACA,QAAA;EACA,WAAA;EACA,eAAA;EACA,iBAAA;EACA,UAAA;EACA,aAAA;EACA,iBAAA;EACA,QAAA;EACA,cAAA;EACA,iBAAA;EACA,aAAA;AACA;", "names": []}